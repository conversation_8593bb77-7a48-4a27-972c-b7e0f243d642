from email.policy import default

from odoo import models, fields,api,_

class RmaLine(models.Model):
    _name = "rma.line"
    _description = "Ligne de retour RMA"

    rma_id = fields.Many2one("rma", required=True)
    rma_id2 = fields.Many2one("rma")
    product_id = fields.Many2one("product.product", required=True)
    lot_id = fields.Many2one("stock.lot", required=True, string="Numéro de série")
    reason_id = fields.Many2one("rma.reason", required=True, string="Raison du retour")
    description = fields.Text("Commentaire")
    state_repair = fields.Selection(
        [
            ("none", "Brouillon"),
            ("received", "Reçu"),
            ("on_repair", "En réparation"),
            ("finished", "Terminé")
        ],
        default='none'
    )


class SockMove(models.Model):
    _inherit = "stock.move"

    rma_id2 = fields.Many2one("rma", string="RMA")
    tracking_uuid = fields.Char('Tracking UUID')

    def action_show_details(self):
        """ Returns an action that will open a form view (in a popup) allowing to work on all the
        move lines of a particular move. This form view is used when "show operations" is not
        checked on the picking type.
        """

        self.ensure_one()
        view = self.env.ref('stock.view_stock_move_operations')
        view = self.env.ref('rma.view_move_line_form')

        return {
            'name': _('Detailed Operations'),
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'stock.move',
            'views': [(view.id, 'form')],
            'view_id': view.id,
            'target': 'new',
            'res_id': self.id,
            'context': dict(
                self.env.context,
            ),
        }


class RmaReason(models.Model):
    _name = "rma.reason"
    _description = "RMA Reason"

    name = fields.Char(required=True)
    description = fields.Text()
