# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* rma
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2023-04-05 07:26+0000\n"
"Last-Translator: <PERSON><PERSON> <nikolaus.weingartmair@grueneerde."
"com>\n"
"Language-Team: none\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.14.1\n"

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma_team.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (Kopie)"

#. module: rma
#: model:ir.actions.report,print_report_name:rma.report_rma_action
msgid "(object._get_report_base_filename())"
msgstr "(object._get_report_base_filename())"

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid ""
"<b>E-mail subject:</b> %(subject)s<br/><br/><b>E-mail body:</b><br/>%(body)s"
msgstr ""
"<b>E-mail Betreff:</b>%(subject)s<br/><br/><b>E-mail body:</b><br/>%(body)s"

#. module: rma
#: model:mail.template,body_html:rma.mail_template_rma_notification
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                    Dear\n"
"                    <t t-out=\"object.partner_id.name\"></t>\n"
"                    <t t-if=\"object.partner_id.parent_id\">\n"
"                        <t t-out=\"object.partner_id.parent_id.name\"></t>\n"
"                    </t>\n"
"                    <br>\n"
"                    <br>\n"
"                    Here is the RMA\n"
"                    <strong>\n"
"                        <t t-out=\"object.name\"></t>\n"
"                    </strong>\n"
"                    from\n"
"                    <t t-out=\"object.company_id.name\"></t>\n"
"                    .\n"
"                    <br>\n"
"                    <br>\n"
"                    Do not hesitate to contact us if you have any question.\n"
"                </p>\n"
"            </div>\n"
"        "
msgstr ""

#. module: rma
#: model:mail.template,body_html:rma.mail_template_rma_receipt_notification
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                    Dear\n"
"                    <t t-out=\"object.partner_id.name\"></t>\n"
"                    <t t-if=\"object.partner_id.parent_id\">\n"
"                        <t t-out=\"object.partner_id.parent_id.name\"></t>\n"
"                    </t>\n"
"                    <br>\n"
"                    <br>\n"
"                    The products for your RMA\n"
"                    <strong>\n"
"                        <t t-out=\"object.name\"></t>\n"
"                    </strong>\n"
"                    from\n"
"                    <t t-out=\"object.company_id.name\"></t>\n"
"                    have been received in our warehouse.\n"
"                    <br>\n"
"                    <br>\n"
"                    Do not hesitate to contact us if you have any question.\n"
"                </p>\n"
"            </div>\n"
"        "
msgstr ""

#. module: rma
#: model:mail.template,body_html:rma.mail_template_rma_draft_notification
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                    Dear\n"
"                    <t t-out=\"object.partner_id.name\"></t>\n"
"                    <t t-if=\"object.partner_id.parent_id\">\n"
"                        <t t-out=\"object.partner_id.parent_id.name\"></t>\n"
"                    </t>\n"
"                    <br>\n"
"                    <br>\n"
"                    You've succesfully placed your RMA\n"
"                    <strong>\n"
"                        <t t-out=\"object.name\"></t>\n"
"                    </strong>\n"
"                    on\n"
"                    <t t-out=\"object.company_id.name\"></t>\n"
"                    . Our team will check it and will validate it as soon as "
"possible.\n"
"                    <br>\n"
"                    <br>\n"
"                    Do not hesitate to contact us if you have any question.\n"
"                </p>\n"
"            </div>\n"
"        "
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<i class=\"fa fa-download\" role=\"img\" aria-label=\"Download\" "
"title=\"Download\"/>"
msgstr ""
"<i class=\"fa fa-download\" role=\"img\" aria-label=\"Download\" "
"title=\"Download\"/>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                                <b>Paid</b>"
msgstr ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                                <b>Bezahlt</b>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<i class=\"fa fa-fw fa-clock-o\"/>\n"
"                                <b>Waiting Payment</b>"
msgstr ""
"<i class=\"fa fa-fw fa-clock-o\"/>\n"
"                                <b>Wartet auf Bezahlung</b>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<i class=\"fa fa-pencil-square-o mr-1\" role=\"img\" aria-label=\"Download\" "
"title=\"Download\"/>"
msgstr ""
"<i class=\"fa fa-pencil-square-o mr-1\" role=\"img\" aria-label=\"Download\" "
"title=\"Download\"/>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<i class=\"fa fa-truck mr-1\" role=\"img\" aria-label=\"Download\" "
"title=\"Download\"/>"
msgstr ""
"<i class=\"fa fa-truck mr-1\" role=\"img\" aria-label=\"Download\" "
"title=\"Download\"/>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<span class=\"badge badge-danger label-text-align\">\n"
"                                            <i class=\"fa fa-fw fa-times\"/"
">\n"
"                                            Cancelled\n"
"                                        </span>"
msgstr ""
"<span class=\"badge badge-danger label-text-align\">\n"
"                                            <i class=\"fa fa-fw fa-times\"/"
">\n"
"                                            Storniert\n"
"                                        </span>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<span class=\"badge badge-danger label-text-align\">\n"
"                                    <i class=\"fa fa-fw fa-times\"/>\n"
"                                    Cancelled\n"
"                                </span>"
msgstr ""
"<span class=\"badge badge-danger label-text-align\">\n"
"                                    <i class=\"fa fa-fw fa-times\"/>\n"
"                                    Abgebrochen\n"
"                                </span>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<span class=\"badge badge-info label-text-align\">\n"
"                                            <i class=\"fa fa-fw fa-clock-o\"/"
">\n"
"                                            Preparation\n"
"                                        </span>"
msgstr ""
"<span class=\"badge badge-info label-text-align\">\n"
"                                            <i class=\"fa fa-fw fa-clock-o\"/"
">\n"
"                                            in Vorbereitung\n"
"                                        </span>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<span class=\"badge badge-info label-text-align\">\n"
"                                    <i class=\"fa fa-fw fa-clock-o\"/>\n"
"                                    Preparation\n"
"                                </span>"
msgstr ""
"<span class=\"badge badge-info label-text-align\">\n"
"                                    <i class=\"fa fa-fw fa-clock-o\"/>\n"
"                                    in Vorbereitung\n"
"                                </span>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<span class=\"badge badge-success label-text-align\">\n"
"                                            <i class=\"fa fa-fw fa-truck\"/"
">\n"
"                                            Shipped\n"
"                                        </span>"
msgstr ""
"<span class=\"badge badge-success label-text-align\">\n"
"                                            <i class=\"fa fa-fw fa-truck\"/"
">\n"
"                                            Versendet\n"
"                                        </span>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<span class=\"badge badge-success label-text-align\">\n"
"                                    <i class=\"fa fa-fw fa-truck\"/>\n"
"                                    Shipped\n"
"                                </span>"
msgstr ""
"<span class=\"badge badge-success label-text-align\">\n"
"                                    <i class=\"fa fa-fw fa-truck\"/>\n"
"                                    Versendet\n"
"                                </span>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<span class=\"badge badge-warning label-text-align\">\n"
"                                            <i class=\"fa fa-fw fa-clock-o\"/"
">\n"
"                                            Partially Available\n"
"                                        </span>"
msgstr ""
"<span class=\"badge badge-warning label-text-align\">\n"
"                                            <i class=\"fa fa-fw fa-clock-o\"/"
">\n"
"                                            nur teilweise verfügbar\n"
"                                        </span>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid ""
"<span class=\"badge badge-warning label-text-align\">\n"
"                                    <i class=\"fa fa-fw fa-clock-o\"/>\n"
"                                    Partially Available\n"
"                                </span>"
msgstr ""
"<span class=\"badge badge-warning label-text-align\">\n"
"                                    <i class=\"fa fa-fw fa-clock-o\"/>\n"
"                                    nur teilweise verfügbar\n"
"                                </span>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Diese Werte sind "
"unternehmensspezifisch.\" groups=\"base.group_multi_company\"/>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong class=\"d-block mb-1\">Delivery</strong>"
msgstr "<strong class=\"d-block mb-1\">Lieferung</strong>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong class=\"d-block mb-1\">Reception</strong>"
msgstr "<strong class=\"d-block mb-1\">Empfang</strong>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong class=\"d-block mb-1\">Refund</strong>"
msgstr "<strong class=\"d-block mb-1\">Rückerstattung</strong>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong>Customer:</strong>"
msgstr "<strong>Kunde:</strong>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "<strong>Date:</strong>"
msgstr "<strong>Datum:</strong>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "<strong>Deadline:</strong>"
msgstr "<strong>Stichtag:</strong>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong>Deadline</strong>"
msgstr "<strong>Stichtag</strong>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong>Delivered quantity</strong>"
msgstr "<strong>Gelieferte Menge</strong>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong>Origin delivery</strong>"
msgstr "<strong>Ursprungslieferung</strong>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "<strong>Origin:</strong>"
msgstr "<strong>Ursprung:</strong>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong>Origin</strong>"
msgstr "<strong>Ursprung</strong>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong>Product</strong>"
msgstr "<strong>Produkt</strong>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong>Quantity</strong>"
msgstr "<strong>Menge</strong>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong>RMA Date</strong>"
msgstr "<strong>RMA Datum</strong>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "<strong>RMA Note:</strong>"
msgstr "<strong>RMA Notiz:</strong>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "<strong>Responsible:</strong>"
msgstr "<strong>Verantwortlich:</strong>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "<strong>Shipping Address:</strong>"
msgstr "<strong>Versandadresse:</strong>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "<strong>Shipping address:</strong>"
msgstr "<strong>Versandadresse:</strong>"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "<strong>State:</strong>"
msgstr "<strong>Status:</strong>"

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Ein Python-Wörterbuch, das ausgewertet wird, um beim Erstellen neuer "
"Datensätze für diesen Alias Standardwerte bereitzustellen."

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_team_view_form
msgid "Accept Emails From"
msgstr "Akzeptiere Emails von"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__access_warning
msgid "Access warning"
msgstr "Zugriffswarnung"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__message_needaction
#: model:ir.model.fields,field_description:rma.field_rma_team__message_needaction
msgid "Action Needed"
msgstr "Handlungsbedarf"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_finalization__active
#: model:ir.model.fields,field_description:rma.field_rma_operation__active
#: model:ir.model.fields,field_description:rma.field_rma_tag__active
#: model:ir.model.fields,field_description:rma.field_rma_team__active
#: model_terms:ir.ui.view,arch_db:rma.rma_finalization_view_search
#: model_terms:ir.ui.view,arch_db:rma.rma_tag_view_search
msgid "Active"
msgstr "Aktiv"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__activity_ids
msgid "Activities"
msgstr "Aktivitäten"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Ausnahmenkennzeichung"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__activity_state
msgid "Activity State"
msgstr "Aktivitätsstatus"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__activity_type_icon
msgid "Activity Type Icon"
msgstr "Aktivitätstyp-Symbol"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_id
msgid "Alias"
msgstr "Alias"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_contact
msgid "Alias Contact Security"
msgstr "Alias Kontaktsicherheit"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_domain_id
msgid "Alias Domain"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_domain
msgid "Alias Domain Name"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_full_name
msgid "Alias Email"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_name
msgid "Alias Name"
msgstr "Alias Name"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_status
msgid "Alias Status"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__alias_status
msgid "Alias status assessed on the last message received."
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_model_id
msgid "Aliased Model"
msgstr "Aliased Model"

#. module: rma
#: model:res.groups,name:rma.group_rma_manual_finalization
msgid "Allow RMA manual finalization"
msgstr "Erlauben Sie den manuellen RMA-Abschluss"

#. module: rma
#: model:ir.model.fields,help:rma.field_res_config_settings__group_rma_manual_finalization
msgid "Allow to finish an RMA without returning back a product or refunding"
msgstr ""
"Lassen Sie eine RMA abschließen, ohne ein Produkt zurückzusenden oder eine "
"Rückerstattung zu leisten"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_finalization_view_search
#: model_terms:ir.ui.view,arch_db:rma.rma_tag_view_search
#: model_terms:ir.ui.view,arch_db:rma.view_rma_finalization_form
msgid "Archived"
msgstr "Archiviert"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Are you sure you want to cancel this RMA"
msgstr "Möchten Sie diese RMA wirklich stornieren"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__message_attachment_count
#: model:ir.model.fields,field_description:rma.field_rma_team__message_attachment_count
msgid "Attachment Count"
msgstr "Anzahl der Anhänge"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_team_view_form
msgid "Avatar"
msgstr "Avatar"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Awaiting Action"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__can_be_finished
msgid "Can Be Finished"
msgstr "Kann beendet werden"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__can_be_locked
msgid "Can Be Locked"
msgstr "Kann gesperrt werden"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__can_be_refunded
msgid "Can Be Refunded"
msgstr "Kann erstattet werden"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__can_be_replaced
msgid "Can Be Replaced"
msgstr "Kann ersetzt werden"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__can_be_returned
msgid "Can Be Returned"
msgstr "Kann zurückgegeben werden"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__can_be_split
msgid "Can Be Split"
msgstr "Kann aufgeteilt werden"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_finalization_wizard_view_form
#: model_terms:ir.ui.view,arch_db:rma.rma_redelivery_wizard_view_form
#: model_terms:ir.ui.view,arch_db:rma.rma_split_wizard_view_form2
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Cancel"
msgstr "Absagen"

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__cancelled
msgid "Canceled"
msgstr "Abgebrochen"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__uom_category_id
msgid "Category"
msgstr "Kastegorie"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__uom_category_id
msgid "Category UoM"
msgstr "Kategorie UoM"

#. module: rma
#: model_terms:ir.actions.act_window,help:rma.rma_action
#: model_terms:ir.actions.act_window,help:rma.rma_team_action
msgid "Click to add a new RMA."
msgstr "Klicken Sie hier, um eine neue RMA hinzuzufügen."

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Closed"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_tag__color
msgid "Color Index"
msgstr "Farbindex"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__commercial_partner_id
msgid "Commercial Entity"
msgstr "Gewerbliche Einheit"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "Communication"
msgstr "Kommunikation"

#. module: rma
#: model:ir.model,name:rma.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__company_id
#: model:ir.model.fields,field_description:rma.field_rma_finalization__company_id
#: model:ir.model.fields,field_description:rma.field_rma_team__company_id
msgid "Company"
msgstr "Unternehmen"

#. module: rma
#: model:ir.model,name:rma.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: rma
#: model:ir.ui.menu,name:rma.rma_configuration_menu
msgid "Configuration"
msgstr "Konfiguration"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Confirm"
msgstr "Bestätigen"

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__confirmed
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Confirmed"
msgstr "Bestätigt"

#. module: rma
#: model:ir.model,name:rma.model_res_partner
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "Contact"
msgstr "Kontakt"

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__uom_category_id
#: model:ir.model.fields,help:rma.field_rma_delivery_wizard__uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Die Umrechnung zwischen Maßeinheiten kann nur erfolgen, wenn sie zur selben "
"Kategorie gehören. Die Umrechnung erfolgt anhand der Verhältnisse."

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_return_picking__create_rma
msgid "Create RMAs"
msgstr "Erstellen Sie RMAs"

#. module: rma
#: model_terms:ir.actions.act_window,help:rma.action_rma_finalization
msgid "Create a new RMA finalization"
msgstr "Erstellen Sie einen neuen RMA-Abschluss"

#. module: rma
#: model_terms:ir.actions.act_window,help:rma.action_rma_tag
msgid "Create a new RMA tag"
msgstr "Erstellen Sie ein neues RMA-Tag"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__create_uid
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__create_uid
#: model:ir.model.fields,field_description:rma.field_rma_finalization__create_uid
#: model:ir.model.fields,field_description:rma.field_rma_finalization_wizard__create_uid
#: model:ir.model.fields,field_description:rma.field_rma_operation__create_uid
#: model:ir.model.fields,field_description:rma.field_rma_split_wizard__create_uid
#: model:ir.model.fields,field_description:rma.field_rma_tag__create_uid
#: model:ir.model.fields,field_description:rma.field_rma_team__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__create_date
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__create_date
#: model:ir.model.fields,field_description:rma.field_rma_finalization__create_date
#: model:ir.model.fields,field_description:rma.field_rma_finalization_wizard__create_date
#: model:ir.model.fields,field_description:rma.field_rma_operation__create_date
#: model:ir.model.fields,field_description:rma.field_rma_split_wizard__create_date
#: model:ir.model.fields,field_description:rma.field_rma_tag__create_date
#: model:ir.model.fields,field_description:rma.field_rma_team__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Benutzerdefinierte Bounce Message"

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#: model:ir.model.fields,field_description:rma.field_rma__partner_id
#, python-format
msgid "Customer"
msgstr "Kunde"

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__access_url
msgid "Customer Portal URL"
msgstr "Kundenportal URL"

#. module: rma
#. odoo-python
#: code:addons/rma/controllers/main.py:0
#: model:ir.model.fields,field_description:rma.field_rma__date
#: model_terms:ir.ui.view,arch_db:rma.portal_my_rmas
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
#, python-format
msgid "Date"
msgstr "Datum"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "Date:"
msgstr "Datum:"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__deadline
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Deadline"
msgstr "Stichtag"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_defaults
msgid "Default Values"
msgstr "Unterlassungswerte"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_redelivery_wizard_view_form
msgid "Deliver"
msgstr "Lieferung"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__delivered_qty
msgid "Delivered Qty"
msgstr "gelieferte Menge"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "Delivered Quantity"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Delivery"
msgstr "Lieferung"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__delivery_picking_count
msgid "Delivery count"
msgstr "Anzahl der Lieferungen"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__delivery_move_ids
msgid "Delivery reservation"
msgstr "Lieferreservierung"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__description
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "Description"
msgstr "Beschreibung"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__display_name
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__display_name
#: model:ir.model.fields,field_description:rma.field_rma_finalization__display_name
#: model:ir.model.fields,field_description:rma.field_rma_finalization_wizard__display_name
#: model:ir.model.fields,field_description:rma.field_rma_operation__display_name
#: model:ir.model.fields,field_description:rma.field_rma_split_wizard__display_name
#: model:ir.model.fields,field_description:rma.field_rma_tag__display_name
#: model:ir.model.fields,field_description:rma.field_rma_team__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__draft
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Draft"
msgstr "Entwurf"

#. module: rma
#: model:mail.message.subtype,name:rma.mt_rma_draft
#: model:mail.message.subtype,name:rma.mt_rma_team_rma_draft
msgid "Draft RMA"
msgstr "RMA Entwurf"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_team_view_form
msgid "Email"
msgstr "Email"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_email
#: model_terms:ir.ui.view,arch_db:rma.rma_team_view_form
msgid "Email Alias"
msgstr "Email Alias"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "Email Template"
msgstr "Email Template"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_company__rma_mail_confirmation_template_id
#: model:ir.model.fields,field_description:rma.field_res_config_settings__rma_mail_confirmation_template_id
msgid "Email Template confirmation for RMA"
msgstr "E-Mail-Vorlage Bestätigung für RMA"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_company__rma_mail_draft_confirmation_template_id
#: model:ir.model.fields,field_description:rma.field_res_config_settings__rma_mail_draft_confirmation_template_id
msgid "Email Template draft notification for RMA"
msgstr "E-Mail-Vorlagenentwurf Benachrichtigung für RMA"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_company__rma_mail_receipt_confirmation_template_id
#: model:ir.model.fields,field_description:rma.field_res_config_settings__rma_mail_receipt_confirmation_template_id
msgid "Email Template receipt confirmation for RMA"
msgstr "E-Mail-Vorlage Empfangsbestätigung für RMA"

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_res_company__rma_mail_confirmation_template_id
#: model:ir.model.fields,help:rma.field_res_config_settings__rma_mail_confirmation_template_id
msgid "Email sent to the customer once the RMA is confirmed."
msgstr "E-Mail wird an den Kunden gesendet, sobald die RMA bestätigt wurde."

#. module: rma
#: model:ir.model.fields,help:rma.field_res_company__rma_mail_receipt_confirmation_template_id
#: model:ir.model.fields,help:rma.field_res_config_settings__rma_mail_receipt_confirmation_template_id
msgid "Email sent to the customer once the RMA products are received."
msgstr ""
"E-Mail wird an die Kunden gesendet, sobald die RMA-Produkte eingegangen sind."

#. module: rma
#: model:ir.model.fields,help:rma.field_res_company__rma_mail_draft_confirmation_template_id
#: model:ir.model.fields,help:rma.field_res_config_settings__rma_mail_draft_confirmation_template_id
msgid "Email sent to the customer when they place an RMA from the portal"
msgstr ""
"Eine E-Mail wird an die Kunden gesendet, wenn eine RMA über das Portal "
"aufgegeben wird"

#. module: rma
#. odoo-python
#: code:addons/rma/wizard/rma_split.py:0
#, python-format
msgid "Extracted RMA"
msgstr "Extrahierter RMA"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__origin_split_rma_id
msgid "Extracted from"
msgstr "Herausgefiltert aus"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__finalization_id
msgid "Finalization Reason"
msgstr "Abschlussgrund"

#. module: rma
#: model:ir.model.constraint,message:rma.constraint_rma_finalization_name_company_uniq
msgid "Finalization name already exists !"
msgstr "Abschlussgrund existiert bereits !"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_finalization_form
msgid "Finish"
msgstr "Abschluss"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_finalization_wizard_view_form
msgid "Finish RMA"
msgstr "RMA abschließen"

#. module: rma
#: model:ir.actions.act_window,name:rma.rma_finalization_wizard_action
msgid "Finish RMA Manualy"
msgstr "RMA manuell abschließen"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_config_settings__group_rma_manual_finalization
msgid "Finish RMA manually choosing a reason"
msgstr "Schließen Sie die RMA manuell ab, indem Sie einen Grund auswählen"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "Finish RMAs manually"
msgstr "Schließen Sie RMAs manuell ab"

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__finished
msgid "Finished"
msgstr "Fertig"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__message_follower_ids
#: model:ir.model.fields,field_description:rma.field_rma_team__message_follower_ids
msgid "Followers"
msgstr "Anhänger"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__message_partner_ids
#: model:ir.model.fields,field_description:rma.field_rma_team__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Partner)"

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Schriftart tolles Symbol z.B. fa-aufgaben"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Group By"
msgstr "Gruppiere nach"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_company__rma_return_grouping
#: model:ir.model.fields,field_description:rma.field_res_config_settings__rma_return_grouping
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__rma_return_grouping
msgid "Group RMA returns by customer address and warehouse"
msgstr "Gruppieren Sie RMA-Rücksendungen nach Kundenadresse und Lager"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "Group RMA returns by customer and warehouse."
msgstr "Gruppieren Sie RMA-Rücksendungen nach Kunde und Lager."

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__has_message
#: model:ir.model.fields,field_description:rma.field_rma_team__has_message
msgid "Has Message"
msgstr "Nachricht vorhanden"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__id
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__id
#: model:ir.model.fields,field_description:rma.field_rma_finalization__id
#: model:ir.model.fields,field_description:rma.field_rma_finalization_wizard__id
#: model:ir.model.fields,field_description:rma.field_rma_operation__id
#: model:ir.model.fields,field_description:rma.field_rma_split_wizard__id
#: model:ir.model.fields,field_description:rma.field_rma_tag__id
#: model:ir.model.fields,field_description:rma.field_rma_team__id
msgid "ID"
msgstr "ID"

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task "
"creation alias)"
msgstr ""
"ID des übergeordneten Datensatzes, der den Alias enthält (Beispiel: Projekt "
"enthält den Alias für die Aufgabenerstellung)"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__activity_exception_icon
msgid "Icon"
msgstr "Icon"

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Symbol zur Anzeige einer Ausnahmeaktivität."

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__message_needaction
#: model:ir.model.fields,help:rma.field_rma_team__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Wenn diese Option aktiviert ist, erfordern neue Nachrichten Ihre "
"Aufmerksamkeit."

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__message_has_error
#: model:ir.model.fields,help:rma.field_rma_team__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Wenn diese Option aktiviert ist, weisen einige Nachrichten einen "
"Übermittlungsfehler auf."

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Wenn eingestellt, wird dieser Inhalt anstelle der Standardnachricht "
"automatisch an nicht autorisierte Benutzer gesendet."

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__active
msgid ""
"If the active field is set to false, it will allow you to hide the RMA Team "
"without removing it."
msgstr ""
"Wenn das aktive Feld auf „false“ gesetzt ist, können Sie das RMA-Team "
"ausblenden, ohne es zu entfernen."

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid "Incoming e-mail"
msgstr "Eingehende E-Mail"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__partner_invoice_id
msgid "Invoice Address"
msgstr "Rechnungsanschrift"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "Invoicing Address:"
msgstr "Rechnungsadresse:"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "Invoicing and Shipping Address:"
msgstr "Rechnungs- und Lieferadresse:"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__message_is_follower
#: model:ir.model.fields,field_description:rma.field_rma_team__message_is_follower
msgid "Is Follower"
msgstr "Ist Follower"

#. module: rma
#: model:ir.model,name:rma.model_account_move
msgid "Journal Entry"
msgstr "Journaleintrag"

#. module: rma
#: model:ir.model,name:rma.model_account_move_line
msgid "Journal Item"
msgstr "Journaleintrag"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__write_uid
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__write_uid
#: model:ir.model.fields,field_description:rma.field_rma_finalization__write_uid
#: model:ir.model.fields,field_description:rma.field_rma_finalization_wizard__write_uid
#: model:ir.model.fields,field_description:rma.field_rma_operation__write_uid
#: model:ir.model.fields,field_description:rma.field_rma_split_wizard__write_uid
#: model:ir.model.fields,field_description:rma.field_rma_tag__write_uid
#: model:ir.model.fields,field_description:rma.field_rma_team__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__write_date
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__write_date
#: model:ir.model.fields,field_description:rma.field_rma_finalization__write_date
#: model:ir.model.fields,field_description:rma.field_rma_finalization_wizard__write_date
#: model:ir.model.fields,field_description:rma.field_rma_operation__write_date
#: model:ir.model.fields,field_description:rma.field_rma_split_wizard__write_date
#: model:ir.model.fields,field_description:rma.field_rma_tag__write_date
#: model:ir.model.fields,field_description:rma.field_rma_team__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Late RMAs"
msgstr "Verspätete RMAs"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__location_id
msgid "Location"
msgstr "Ort"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Lock"
msgstr "Sperren"

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__locked
msgid "Locked"
msgstr "Gesperrt"

#. module: rma
#: model_terms:ir.actions.act_window,help:rma.action_rma_finalization
msgid ""
"Manage RMA finalization reasons to better classify them for tracking and "
"analysis purposes."
msgstr ""
"Verwalten Sie RMA-Abschlussgründe, um sie für Nachverfolgungs- und "
"Analysezwecke besser zu klassifizieren."

#. module: rma
#: model_terms:ir.actions.act_window,help:rma.action_rma_tag
msgid ""
"Manage RMA tags to better classify them for tracking and analysis purposes."
msgstr ""
"Verwalten Sie RMA-Tags, um sie für Nachverfolgungs- und Analysezwecke besser "
"zu klassifizieren."

#. module: rma
#: model:ir.module.category,description:rma.rma_module_category
msgid "Manage Return Merchandise Authorizations (RMAs)."
msgstr "Verwalten Sie Warenrücksendegenehmigungen (RMAs)."

#. module: rma
#: model:res.groups,name:rma.rma_group_manager
msgid "Manager"
msgstr "Manager"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__message_has_error
#: model:ir.model.fields,field_description:rma.field_rma_team__message_has_error
msgid "Message Delivery error"
msgstr "Fehler bei der Nachrichtenübermittlung"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__message_ids
#: model:ir.model.fields,field_description:rma.field_rma_team__message_ids
msgid "Messages"
msgstr "Mitteilungen"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Meine Aktivitätsfrist"

#. module: rma
#. odoo-python
#: code:addons/rma/controllers/main.py:0
#: model:ir.model.fields,field_description:rma.field_rma__name
#: model:ir.model.fields,field_description:rma.field_rma_operation__name
#: model:ir.model.fields,field_description:rma.field_rma_team__name
#, python-format
msgid "Name"
msgstr "Name"

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid "New"
msgstr "Neu"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Frist für die nächste Aktivität"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__activity_summary
msgid "Next Activity Summary"
msgstr "Zusammenfassung der nächsten Aktivität"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__activity_type_id
msgid "Next Activity Type"
msgstr "Nächster Aktivitätstyp"

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid "None of the selected RMAs can perform a replacement."
msgstr "Keiner der ausgewählten RMAs kann einen Austausch durchführen."

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid "None of the selected RMAs can perform a return."
msgstr "Keine der ausgewählten RMAs kann eine Rücksendung durchführen."

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__priority__0
msgid "Normal"
msgstr "Normal"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__message_needaction_counter
#: model:ir.model.fields,field_description:rma.field_rma_team__message_needaction_counter
msgid "Number of Actions"
msgstr "Anzahl der Aktionen"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__message_has_error_counter
#: model:ir.model.fields,field_description:rma.field_rma_team__message_has_error_counter
msgid "Number of errors"
msgstr "Anzahl der Fehler"

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__message_needaction_counter
#: model:ir.model.fields,help:rma.field_rma_team__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__message_has_error_counter
#: model:ir.model.fields,help:rma.field_rma_team__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Anzahl der Nachrichten mit Zustellfehler"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_return_picking_line__rma_operation_id
msgid "Operation"
msgstr ""

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Optionale ID eines Threads (Datensatzes), an den alle eingehenden "
"Nachrichten angehängt werden, auch wenn sie nicht darauf geantwortet haben. "
"Wenn gesetzt, wird die Erstellung neuer Datensätze vollständig deaktiviert."

#. module: rma
#: model:ir.ui.menu,name:rma.rma_orders_menu
msgid "Orders"
msgstr "Aufträge"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__picking_id
msgid "Origin Delivery"
msgstr "Ursprungslieferung"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "Origin delivery"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__move_id
msgid "Origin move"
msgstr "Ursprungsbuchungssatz"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Other Information"
msgstr "Andere Informationen"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_parent_model_id
msgid "Parent Model"
msgstr "übergeordnetes Modell"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Thread-ID des übergeordneten Datensatzes"

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not "
"necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Übergeordnetes Modell mit dem Alias. Das Modell, das die Alias-Referenz "
"enthält, ist nicht unbedingt das durch alias_model_id angegebene Modell "
"(Beispiel: Projekt (parent_model) und Aufgabe (model))"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Partner"
msgstr "Partner"

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following "
"channels\n"
msgstr ""
"Richtlinie zum Posten einer Nachricht im Dokument über das Mailgateway.\n"
"- Jeder: Jeder kann Posten\n"
"- Partner: Nur authentifizierte Partner\n"
"- Follower: Nur Follower des zugehörigen Dokuments oder Mitglieder der "
"folgenden Kanäle\n"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__access_url
msgid "Portal Access URL"
msgstr "URL für den Portalzugriff"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Preview"
msgstr "Vorschau"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__priority
msgid "Priority"
msgstr "Priorität"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Processed"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__procurement_group_id
msgid "Procurement group"
msgstr "Beschaffungsgruppe"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__product_id
#: model_terms:ir.ui.view,arch_db:rma.portal_my_rmas
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "Product"
msgstr "Produkt"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__product_uom_qty
msgid "Product qty"
msgstr "Produktmenge"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_tag__is_public
msgid "Public Tag"
msgstr "Öffentlicher Tag"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__product_uom_qty
#: model_terms:ir.ui.view,arch_db:rma.portal_my_rmas
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "Quantity"
msgstr "Menge"

#. module: rma
#. odoo-python
#: code:addons/rma/wizard/rma_delivery.py:0
#: model:ir.model.constraint,message:rma.constraint_rma_split_wizard_check_product_uom_qty_positive
#, python-format
msgid "Quantity must be greater than 0."
msgstr "Die Menge muss größer als 0 sein."

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_split_wizard__product_uom_qty
msgid "Quantity to extract"
msgstr "Zu extrahierende Menge"

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid ""
"Quantity to extract cannot be greater than remaining delivery quantity "
"(%(remaining_qty)s %(product_uom)s)"
msgstr ""
"Die zu entnehmende Menge darf nicht größer als die verbleibende Liefermenge "
"sein (%(remaining_qty)s%(product_uom)s)"

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_split_wizard__product_uom_qty
msgid "Quantity to extract to a new RMA."
msgstr "Menge, die in eine neue RMA extrahiert werden soll."

#. module: rma
#: model:ir.actions.act_window,name:rma.rma_action
#: model:ir.model,name:rma.model_rma
#: model:ir.model.fields,field_description:rma.field_account_move_line__rma_id
#: model:ir.model.fields,field_description:rma.field_rma_split_wizard__rma_id
#: model:ir.model.fields,field_description:rma.field_stock_warehouse__rma
#: model:ir.module.category,name:rma.rma_module_category
#: model:ir.ui.menu,name:rma.rma_menu
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:rma.view_partner_form
#: model_terms:ir.ui.view,arch_db:rma.view_picking_form
msgid "RMA"
msgstr "RMA"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_my_rmas
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "RMA #"
msgstr "RMA #"

#. module: rma
#. odoo-python
#: code:addons/rma/models/res_company.py:0
#, python-format
msgid "RMA Code"
msgstr "RMA-Code"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "RMA Confirmation Email"
msgstr "RMA-Bestätigungs-E-Mail"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "RMA Date"
msgstr "RMA-Datum"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "RMA Deadline"
msgstr "RMA-Frist"

#. module: rma
#. odoo-python
#: code:addons/rma/models/stock_warehouse.py:0
#, python-format
msgid "RMA Delivery Orders"
msgstr "RMA-Lieferaufträge"

#. module: rma
#: model:ir.model,name:rma.model_rma_delivery_wizard
msgid "RMA Delivery Wizard"
msgstr "RMA-Zustellungsassistent"

#. module: rma
#: model:mail.template,name:rma.mail_template_rma_draft_notification
msgid "RMA Draft Notification"
msgstr "Benachrichtigung über RMA-Entwurf"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.view_rma_finalization_form
msgid "RMA Finalization"
msgstr "RMA-Abschluss"

#. module: rma
#: model:ir.model,name:rma.model_rma_finalization
msgid "RMA Finalization Reason"
msgstr "RMA-Abschlussgrund"

#. module: rma
#: model:ir.actions.act_window,name:rma.action_rma_finalization
#: model:ir.ui.menu,name:rma.rma_configuration_rma_finalization_menu
#: model_terms:ir.ui.view,arch_db:rma.rma_finalization_view_search
msgid "RMA Finalization Reasons"
msgstr "Gründe für den RMA-Abschluss"

#. module: rma
#: model:ir.model,name:rma.model_rma_finalization_wizard
msgid "RMA Finalization Wizard"
msgstr "RMA-Abschlussassistent"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_warehouse__rma_in_type_id
msgid "RMA In Type"
msgstr "RMA-Eingangstyp"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_warehouse__rma_loc_id
msgid "RMA Location"
msgstr "RMA-Standort"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "RMA Manual Finalization"
msgstr "Manueller RMA-Abschluss"

#. module: rma
#: model:mail.message.subtype,name:rma.mt_rma_notification
#: model:mail.message.subtype,name:rma.mt_rma_team_rma_notification
#: model:mail.template,name:rma.mail_template_rma_notification
msgid "RMA Notification"
msgstr "RMA-Benachrichtigung"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_rma_page
msgid "RMA Order -"
msgstr "RMA-Bestellung -"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.portal_my_home_menu_rma
#: model_terms:ir.ui.view,arch_db:rma.portal_my_home_rma
#: model_terms:ir.ui.view,arch_db:rma.portal_my_rmas
msgid "RMA Orders"
msgstr "RMA-Bestellungen"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_warehouse__rma_out_type_id
msgid "RMA Out Type"
msgstr "RMA-Out-Typ"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "RMA Receipt Confirmation Email"
msgstr "RMA-Empfangsbestätigungs-E-Mail"

#. module: rma
#: model:mail.template,name:rma.mail_template_rma_receipt_notification
msgid "RMA Receipt Notification"
msgstr "RMA-Empfangsbenachrichtigung"

#. module: rma
#. odoo-python
#: code:addons/rma/models/stock_warehouse.py:0
#, python-format
msgid "RMA Receipts"
msgstr "RMA-Quittungen"

#. module: rma
#: model:ir.actions.report,name:rma.report_rma_action
msgid "RMA Report"
msgstr "RMA-Bericht"

#. module: rma
#: model:ir.model,name:rma.model_rma_split_wizard
msgid "RMA Split Wizard"
msgstr "RMA-Aufteilungsassistent"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.view_rma_tag_form
msgid "RMA Tag"
msgstr "RMA Tag"

#. module: rma
#: model:ir.actions.act_window,name:rma.action_rma_tag
#: model:ir.model,name:rma.model_rma_tag
#: model:ir.ui.menu,name:rma.rma_configuration_rma_tag_menu
#: model_terms:ir.ui.view,arch_db:rma.rma_tag_view_search
msgid "RMA Tags"
msgstr "RMA-Tags"

#. module: rma
#: model:ir.model,name:rma.model_rma_team
#: model:ir.model.fields,field_description:rma.field_res_users__rma_team_id
#: model:ir.ui.menu,name:rma.rma_configuration_rma_team_menu
#: model_terms:ir.ui.view,arch_db:rma.rma_team_view_form
msgid "RMA Team"
msgstr "RMA-Team"

#. module: rma
#: model:ir.model.fields,help:rma.field_res_users__rma_team_id
msgid "RMA Team the user is member of."
msgstr "RMA-Team, dem der Benutzer angehört."

#. module: rma
#: model:mail.message.subtype,description:rma.mt_rma_notification
msgid "RMA automatic customer notifications"
msgstr "Automatische RMA-Kundenbenachrichtigungen"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_partner__rma_count
#: model:ir.model.fields,field_description:rma.field_res_users__rma_count
#: model:ir.model.fields,field_description:rma.field_stock_picking__rma_count
msgid "RMA count"
msgstr "RMA-Zählung"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "RMA draft notification Email"
msgstr "Benachrichtigungs-E-Mail zum RMA-Entwurf"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_warehouse__rma_in_route_id
msgid "RMA in Route"
msgstr ""

#. module: rma
#: model:mail.message.subtype,description:rma.mt_rma_draft
msgid "RMA in draft state"
msgstr "RMA im Entwurfszustand"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_warehouse__rma_out_route_id
msgid "RMA out Route"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_move__rma_receiver_ids
msgid "RMA receivers"
msgstr "RMA-Empfänger"

#. module: rma
#: model:ir.model.fields,help:rma.field_stock_warehouse__rma
msgid "RMA related products can be stored in this warehouse."
msgstr "RMA-bezogene Produkte können in diesem Lager gelagert werden."

#. module: rma
#: model:ir.model,name:rma.model_rma_operation
msgid "RMA requested operation"
msgstr "RMA angeforderter Vorgang"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_move__rma_id
msgid "RMA return"
msgstr "RMA-Rückgabe"

#. module: rma
#: model:ir.actions.act_window,name:rma.rma_team_action
#: model:ir.model.fields,field_description:rma.field_rma__team_id
msgid "RMA team"
msgstr "RMA-Team"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_partner__rma_ids
#: model:ir.model.fields,field_description:rma.field_res_users__rma_ids
#: model:ir.model.fields,field_description:rma.field_stock_move__rma_ids
msgid "RMAs"
msgstr "RMAs"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "RMAs which deadline has passed"
msgstr "RMAs, deren Frist abgelaufen ist"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "RMAs yet to be fully processed"
msgstr "RMAs müssen noch vollständig bearbeitet werden"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_finalization_wizard__finalization_id
msgid "Reason"
msgstr "Grund"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_finalization__name
msgid "Reason Name"
msgstr "Name des Grundes"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Receipt"
msgstr "Rückgabebeleg"

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__received
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Received"
msgstr "Erhalten"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__reception_move_id
msgid "Reception move"
msgstr "Eingangsbuchungssatz"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Thread ID des Eintrags"

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__origin
msgid "Reference of the document that generated this RMA."
msgstr "Referenz des Dokuments, das diese RMA erstellt hat."

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#: model:ir.model.fields,field_description:rma.field_rma__refund_id
#: model:rma.operation,name:rma.rma_operation_refund
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
#, python-format
msgid "Refund"
msgstr "Erstattung"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__refund_line_id
msgid "Refund Line"
msgstr "Rückerstattungsbuchungssatz"

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__partner_invoice_id
msgid "Refund address for current RMA."
msgstr "Rückerstattungsadresse für aktuelle RMA."

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__refunded
msgid "Refunded"
msgstr "Erstattet"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__remaining_qty
msgid "Remaining delivered qty"
msgstr "Verbleibende Liefermenge"

#. module: rma
#: model:rma.operation,name:rma.rma_operation_return
msgid "Repair"
msgstr "Reparatur"

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma_delivery_wizard__type__replace
#: model:rma.operation,name:rma.rma_operation_replace
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Replace"
msgstr "Ersetzen"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__product_id
msgid "Replace Product"
msgstr "Produkt ersetzen"

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__replaced
msgid "Replaced"
msgstr "Ersetzt"

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid ""
"Replacement: Move <a href=\"#\" data-oe-model=\"stock.move\" data-oe-"
"id=\"%(move_id)d\">%(move_name)s</a> (Picking <a href=\"#\" data-oe-"
"model=\"stock.picking\" data-oe-id=\"%(picking_id)d\"> %(picking_name)s</a>) "
"has been created."
msgstr ""
"Ersatz: Buchung <a href=\"#\" data-oe-model=\"stock.move\" data-oe-"
"id=\"%(move_id)d\">%(move_name)s</a> (Picking <a href=\"#\" data-oe-"
"model=\"stock.picking\" data-oe-id=\"%(picking_id)d\">%(picking_name)s</a>) "
"wurde erstellt.."

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid ""
"Replacement:<br/>Product <a href=\"#\" data-oe-model=\"product.product\" "
"data-oe-id=\"%(id)d\">%(name)s</a><br/>Quantity %(qty)s %(uom)s<br/>This "
"replacement did not create a new move, but one of the previously created "
"moves was updated with this data."
msgstr ""
"Ersatz:<br/>Produkt <a href=\"#\" data-oe-model=\"product.product\" data-oe-"
"id=\"%(id)d\">%(name)s</a><br/>Quantity %(qty)s%(uom)s<br/>Durch diese "
"Ersetzung wurde kein neuer Buchungssatz erstellt, aber einer der zuvor "
"erstellten Buchungssätze wurde mit diesen Daten aktualisiert."

#. module: rma
#: model:ir.ui.menu,name:rma.rma_reporting_menu
msgid "Reporting"
msgstr "Berichte"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__operation_id
#: model:ir.model.fields,field_description:rma.field_stock_return_picking__rma_operation_id
#: model_terms:ir.ui.view,arch_db:rma.report_rma_document
msgid "Requested operation"
msgstr "Angeforderter Vorgang"

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid "Required field(s):%s"
msgstr "Erforderliche(s) Feld(er): %s"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__user_id
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Responsible"
msgstr "Verantwortlich"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__activity_user_id
msgid "Responsible User"
msgstr "Verantwortlicher Benutzer"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_return_picking__location_id
msgid "Return Location"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "Return Merchandise Authorization Management"
msgstr ""

#. module: rma
#: model:ir.model,name:rma.model_stock_return_picking
msgid "Return Picking"
msgstr "Inbound Kommissionierung"

#. module: rma
#: model:ir.model,name:rma.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr ""

#. module: rma
#: model:ir.actions.act_window,name:rma.rma_delivery_wizard_action
#: model:ir.model.fields.selection,name:rma.selection__rma_delivery_wizard__type__return
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Return to customer"
msgstr "Zurück zum Kunden"

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid ""
"Return: <a href=\"#\" data-oe-model=\"stock.picking\" data-oe-"
"id=\"%(id)d\">%(name)s</a> has been created."
msgstr ""
"Rückgabe: <a href=\"#\" data-oe-model=\"stock.picking\" data-oe-"
"id=\"%(id)d\">%(name)s</a> wurde erstellt."

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__returned
msgid "Returned"
msgstr "Ist zurückgekommen"

#. module: rma
#. odoo-python
#: code:addons/rma/wizard/stock_picking_return.py:0
#, python-format
msgid "Returned Picking"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_tag__rma_ids
msgid "Rma"
msgstr "Rma"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__rma_count
msgid "Rma Count"
msgstr "Anzahl RMA"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_return_picking__rma_location_ids
msgid "Rma Location"
msgstr ""

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__scheduled_date
msgid "Scheduled Date"
msgstr "Geplantes Datum"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__access_token
msgid "Security Token"
msgstr "Sicherheitstoken"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_company__send_rma_confirmation
#: model:ir.model.fields,field_description:rma.field_res_config_settings__send_rma_confirmation
msgid "Send RMA Confirmation"
msgstr "RMA-Bestätigung senden"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_company__send_rma_receipt_confirmation
#: model:ir.model.fields,field_description:rma.field_res_config_settings__send_rma_receipt_confirmation
msgid "Send RMA Receipt Confirmation"
msgstr "RMA-Empfangsbestätigung senden"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_res_company__send_rma_draft_confirmation
#: model:ir.model.fields,field_description:rma.field_res_config_settings__send_rma_draft_confirmation
msgid "Send RMA draft Confirmation"
msgstr "RMA-Bestätigungsentwurf senden"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "Send automatic RMA info to customer"
msgstr "Senden Sie automatische RMA-Informationen an den Kunden"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "Send automatic RMA products reception notification to customer"
msgstr ""
"Senden Sie eine automatische Benachrichtigung über den Empfang von RMA-"
"Produkten an den Kunden"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "Send automatic notification when the customer places an RMA"
msgstr ""
"Senden Sie eine automatische Benachrichtigung, wenn der Kunde eine RMA "
"aufgibt"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Send by Email"
msgstr "Per E-Mail gesendet"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Send by Mail"
msgstr "Per Post versenden"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__sent
msgid "Sent"
msgstr "Gesendet"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__sequence
msgid "Sequence"
msgstr "Sequenz"

#. module: rma
#. odoo-python
#: code:addons/rma/models/stock_warehouse.py:0
#, python-format
msgid "Sequence RMA in"
msgstr "Reihenfolge RMA ein"

#. module: rma
#. odoo-python
#: code:addons/rma/models/stock_warehouse.py:0
#, python-format
msgid "Sequence RMA out"
msgstr "Sequenz RMA outgoing"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Set to draft"
msgstr "Auf Entwurf setzen"

#. module: rma
#: model:ir.actions.act_window,name:rma.action_rma_config_settings
#: model:ir.ui.menu,name:rma.menu_rma_general_settings
msgid "Settings"
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Share"
msgstr "Teilen"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__partner_shipping_id
msgid "Shipping Address"
msgstr "Lieferanschrift"

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__partner_shipping_id
msgid "Shipping address for current RMA."
msgstr "Lieferadresse für aktuelle RMA."

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__origin
msgid "Source Document"
msgstr "Quelldokument"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_split_wizard_view_form2
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Split"
msgstr "Split"

#. module: rma
#: model:ir.actions.act_window,name:rma.rma_split_wizard_action
msgid "Split RMA"
msgstr "Geteilter RMA"

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid ""
"Split: <a href=\"#\" data-oe-model=\"rma\" data-oe-id=\"%(id)d\">%(name)s</"
"a> has been created."
msgstr ""
"Split: <a href=\"#\" data-oe-model=\"rma\" data-oe-id=\"%(id)d\">%(name)s</"
"a> wurde erstellt."

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__state
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "State"
msgstr "Status"

#. module: rma
#. odoo-python
#: code:addons/rma/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:rma.portal_my_rmas
#, python-format
msgid "Status"
msgstr "Status"

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status basierend auf Aktivitäten\n"
"Überfällig: Fälligkeitsdatum ist bereits überschritten\n"
"Heute: Aktivitätsdatum ist heute\n"
"Geplant: Zukünftige Aktivitäten."

#. module: rma
#: model:ir.model,name:rma.model_stock_move
msgid "Stock Move"
msgstr "Lagerbewegungen"

#. module: rma
#: model:ir.model,name:rma.model_stock_rule
msgid "Stock Rule"
msgstr "Lagerregel"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_tag__name
msgid "Tag Name"
msgstr "Tag Name"

#. module: rma
#: model:ir.model.constraint,message:rma.constraint_rma_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Tag-Name existiert bereits!"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__tag_ids
msgid "Tags"
msgstr "Tags"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Tags..."
msgstr "Tags..."

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__user_id
msgid "Team Leader"
msgstr "Teamleiter"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_team__member_ids
#: model_terms:ir.ui.view,arch_db:rma.rma_team_view_form
msgid "Team Members"
msgstr "Teammitglieder"

#. module: rma
#: model:ir.model.constraint,message:rma.constraint_rma_operation_name_uniq
msgid "That operation name already exists !"
msgstr "Dieser Vorgangsname existiert bereits!"

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_tag__active
msgid "The active field allows you to hide the category without removing it."
msgstr ""
"Das aktive Feld ermöglicht es Ihnen, die Kategorie auszublenden, ohne sie zu "
"entfernen."

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming "
"email that does not reply to an existing record will cause the creation of a "
"new record of this model (e.g. a Project Task)"
msgstr ""
"Das Modell (Art des Odoo-Dokuments), dem dieser Alias entspricht. Jede "
"eingehende E-Mail, die nicht auf einen vorhandenen Datensatz antwortet, "
"führt zur Erstellung eines neuen Datensatzes dieses Modells (z. B. einer "
"Projektaufgabe)."

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_team__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Der Name des E-Mail-Alias, z. „Jobs“, wenn Sie E-Mails für <jobs@example."
"odoo.com> abfangen möchten"

#. module: rma
#. odoo-python
#: code:addons/rma/models/stock_move.py:0
#, python-format
msgid ""
"The quantity done for the product '%(id)s' must be equal to its initial "
"demand because the stock move is linked to an RMA (%(name)s)."
msgstr ""
"Die für das Produkt '%(id)s' fertig gestellte Menge muss gleich der "
"ursprünglichen Nachfrage sein, da die Bestandsumlagerung mit einer RMA "
"(%(name)s) verknüpft ist."

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid "The quantity to return is greater than remaining quantity."
msgstr "Die zurückzugebende Menge ist größer als die verbleibende Menge."

#. module: rma
#: model:ir.model.fields,help:rma.field_rma_tag__is_public
msgid "The tag is visible in the portal view"
msgstr "Das Tag ist in der Portalansicht sichtbar"

#. module: rma
#. odoo-python
#: code:addons/rma/models/account_move.py:0
#, python-format
msgid ""
"There is at least one invoice lines whose quantity is less than the quantity "
"specified in its linked RMA."
msgstr ""
"Es gibt mindestens einen Rechnungsposten, dessen Menge geringer ist als die "
"Menge, die in der verknüpften RMA angegeben ist."

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid "This RMA cannot be split."
msgstr "Diese RMA kann nicht aufgeteilt werden."

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid "This RMA cannot perform a replacement."
msgstr "Diese RMA kann keinen Ersatz leisten."

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid "This RMA cannot perform a return."
msgstr "Diese RMA kann keine Rücksendung durchführen."

#. module: rma
#: model:ir.actions.server,name:rma.rma_refund_action_server
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "To Refund"
msgstr "Rückerstattung"

#. module: rma
#: model:ir.model,name:rma.model_stock_picking
msgid "Transfer"
msgstr "Transfer"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__type
msgid "Type"
msgstr "Typ"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_stock_return_picking__picking_type_code
msgid "Type of Operation"
msgstr "Art der Operation"

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Art der aufgezeichneten Ausnahmeaktivität."

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Unassigned RMAs"
msgstr "Nicht zugewiesene RMAs"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__product_uom
#: model:ir.model.fields,field_description:rma.field_rma_split_wizard__product_uom
msgid "Unit of measure"
msgstr "Maßeinheit"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_form
msgid "Unlock"
msgstr "Freischalten"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.rma_view_search
msgid "Unresolved RMAs"
msgstr "Ungelöste RMAs"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__product_uom
msgid "UoM"
msgstr "UoM (ME)"

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__priority__1
msgid "Urgent"
msgstr "Dringend"

#. module: rma
#: model:ir.model,name:rma.model_res_users
msgid "User"
msgstr ""

#. module: rma
#: model:res.groups,name:rma.rma_group_user_all
msgid "User: All Documents"
msgstr "Benutzer: Alle Dokumente"

#. module: rma
#: model:res.groups,name:rma.rma_group_user_own
msgid "User: Own Documents Only"
msgstr "Benutzer: Nur eigene Dokumente"

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__waiting_replacement
msgid "Waiting for replacement"
msgstr "Warten auf Ersatz"

#. module: rma
#: model:ir.model.fields.selection,name:rma.selection__rma__state__waiting_return
msgid "Waiting for return"
msgstr "Warten auf Rückkehr"

#. module: rma
#: model:ir.model,name:rma.model_stock_warehouse
#: model:ir.model.fields,field_description:rma.field_rma__warehouse_id
#: model:ir.model.fields,field_description:rma.field_rma_delivery_wizard__warehouse_id
msgid "Warehouse"
msgstr "Lager"

#. module: rma
#: model:ir.model.fields,field_description:rma.field_rma__website_message_ids
#: model:ir.model.fields,field_description:rma.field_rma_team__website_message_ids
msgid "Website Messages"
msgstr "Website-Nachrichten"

#. module: rma
#: model:ir.model.fields,help:rma.field_rma__website_message_ids
#: model:ir.model.fields,help:rma.field_rma_team__website_message_ids
msgid "Website communication history"
msgstr "Kommunikationsgeschichte der Website"

#. module: rma
#: model:ir.model.fields,help:rma.field_res_company__send_rma_draft_confirmation
#: model:ir.model.fields,help:rma.field_res_config_settings__send_rma_draft_confirmation
msgid "When a customer places an RMA, send a notification with it"
msgstr "Wenn ein Kunde eine RMA aufgibt, senden Sie eine Benachrichtigung mit"

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid ""
"When customers themselves place an RMA from the portal, send an automatic "
"notification acknowleging it."
msgstr ""
"Wenn Kunden selbst eine RMA über das Portal aufgeben, senden Sie eine "
"automatische Benachrichtigung, die dies bestätigt."

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid "When the RMA is confirmed, send an automatic information email."
msgstr ""
"Wenn die RMA bestätigt ist, senden Sie eine automatische Informations-E-Mail."

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid ""
"When the RMA is receive, allow to finsish it manually choosing\n"
"                                    a finalization reason."
msgstr ""

#. module: rma
#: model_terms:ir.ui.view,arch_db:rma.res_config_settings_view_form
msgid ""
"When the RMA products are received, send an automatic information email."
msgstr ""
"Senden Sie nach Erhalt der RMA-Produkte eine automatische Informations-E-"
"Mail."

#. module: rma
#: model:ir.model.fields,help:rma.field_res_company__send_rma_receipt_confirmation
#: model:ir.model.fields,help:rma.field_res_config_settings__send_rma_receipt_confirmation
msgid ""
"When the RMA receipt is confirmed, send a confirmation email to the customer."
msgstr ""
"Wenn der RMA-Eingang bestätigt ist, senden Sie eine Bestätigungs-E-Mail an "
"den Kunden."

#. module: rma
#: model:ir.model.fields,help:rma.field_res_company__send_rma_confirmation
#: model:ir.model.fields,help:rma.field_res_config_settings__send_rma_confirmation
msgid ""
"When the delivery is confirmed, send a confirmation email to the customer."
msgstr ""
"Wenn die Lieferung bestätigt ist, senden Sie eine Bestätigungs-E-Mail an den "
"Kunden."

#. module: rma
#. odoo-python
#: code:addons/rma/models/rma.py:0
#, python-format
msgid "You cannot delete RMAs that are not in draft state"
msgstr ""
"RMAs, die sich nicht im Entwurfsstatus befinden, können nicht gelöscht werden"

#. module: rma
#. odoo-python
#: code:addons/rma/wizard/stock_picking_return.py:0
#, python-format
msgid ""
"You must specify the 'Customer' in the 'Stock Picking' from which RMAs will "
"be created"
msgstr ""
"Sie müssen den „Kunden“ in der „Lagerkommissionierung“ angeben, von dem RMAs "
"erstellt werden"

#. module: rma
#: model:res.groups,comment:rma.rma_group_user_all
msgid ""
"the user will have access to all records of everyone in the RMA application."
msgstr ""
"Der Benutzer hat Zugriff auf alle Aufzeichnungen aller Personen in der RMA-"
"Anwendung."

#. module: rma
#: model:res.groups,comment:rma.rma_group_user_own
msgid "the user will have access to his own data in the RMA application."
msgstr "Der Benutzer hat Zugriff auf seine eigenen Daten in der RMA-Anwendung."

#. module: rma
#: model:res.groups,comment:rma.rma_group_manager
msgid ""
"the user will have an access to the RMA configuration as well as statistic "
"reports."
msgstr ""
"Der Benutzer hat Zugriff auf die RMA-Konfiguration sowie auf "
"Statistikberichte."

#. module: rma
#: model:mail.template,subject:rma.mail_template_rma_notification
msgid "{{object.company_id.name}} RMA (Ref {{object.name or 'n/a' }})"
msgstr "{{object.company_id.name}} RMA (Ref {{object.name or 'n/a' }})"

#. module: rma
#: model:mail.template,subject:rma.mail_template_rma_receipt_notification
msgid ""
"{{object.company_id.name}} RMA (Ref {{object.name or 'n/a' }}) products "
"received"
msgstr ""
"{{object.company_id.name}} RMA-Produkte (Ref {{object.name or 'n/a' }}) "
"erhalten"

#. module: rma
#: model:mail.template,subject:rma.mail_template_rma_draft_notification
msgid ""
"{{object.company_id.name}} Your RMA has been succesfully created (Ref "
"{{object.name or 'n/a' }})"
msgstr ""
"{{object.company_id.name}} Ihre RMA wurde erfolgreich erstellt (Ref {{object."
"name or 'n/a' }})"

#~ msgid "<strong>Delivered qty:</strong>"
#~ msgstr "<strong>Liefermenge:</strong>"

#~ msgid "<strong>Move:</strong>"
#~ msgstr "<strong>Buchungssatz:</strong>"

#~ msgid "<strong>Origin delivery:</strong>"
#~ msgstr "<strong>Ursprungslieferung:</strong>"

#~ msgid "<strong>Product:</strong>"
#~ msgstr "<strong>Produkt:</strong>"

#~ msgid "<strong>Quantity:</strong>"
#~ msgstr "<strong>Menge:</strong>"

#~ msgid "SMS Delivery error"
#~ msgstr "SMS-Zustellungsfehler"

#~ msgid "Alias domain"
#~ msgstr "Alias domain"

#~ msgid "Delivered Qty Done"
#~ msgstr "gelieferte Menge Erledigt"

#~ msgid "Last Modified on"
#~ msgstr "Zuletzt geändert am"

#~ msgid "Main Attachment"
#~ msgstr "Hauptanhang"

#~ msgid "Owner"
#~ msgstr "Eigentümer"

#~ msgid "Remaining delivered qty to done"
#~ msgstr "Verbleibende Liefermenge zu erledigen"

#~ msgid ""
#~ "The owner of records created upon receiving emails on this alias. If this "
#~ "field is not set the system will attempt to find the right owner based on "
#~ "the sender (From) address, or will use the Administrator account if no "
#~ "system user is found for that address."
#~ msgstr ""
#~ "Der Eigentümer von Datensätzen, die beim Empfang von E-Mails unter diesem "
#~ "Alias erstellt wurden. Wenn dieses Feld nicht gesetzt ist, versucht das "
#~ "System, den richtigen Eigentümer anhand der Absenderadresse (Von) zu "
#~ "finden, oder verwendet das Administratorkonto, wenn kein Systembenutzer "
#~ "für diese Adresse gefunden wird."

#~ msgid ""
#~ "When the RMA is receive, allow to finsish it manually choosing\n"
#~ "                            a finalization reason."
#~ msgstr ""
#~ "Wenn die RMA eingegangen ist, können Sie sie manuell abschließen, \n"
#~ "                               indem Sie einen Abschlussgrund auswählen."

#~ msgid "{{(object.name or '')}}"
#~ msgstr "{{(object.name or '')}}"

#~ msgid ""
#~ "<div style=\"margin: 0px; padding: 0px;\">\n"
#~ "                <p style=\"margin: 0px; padding: 0px; font-size: 13px;"
#~ "\">\n"
#~ "                    Dear\n"
#~ "                    <t t-out=\"object.partner_id.name\"/>\n"
#~ "                    <t t-if=\"object.partner_id.parent_id\">\n"
#~ "                        <t t-out=\"object.partner_id.parent_id.name\"/>\n"
#~ "                    </t>\n"
#~ "                    <br/>\n"
#~ "                    <br/>\n"
#~ "                    Here is the RMA\n"
#~ "                    <strong>\n"
#~ "                        <t t-out=\"object.name\"/>\n"
#~ "                    </strong>\n"
#~ "                    from\n"
#~ "                    <t t-out=\"object.company_id.name\"/>\n"
#~ "                    .\n"
#~ "                    <br/>\n"
#~ "                    <br/>\n"
#~ "                    Do not hesitate to contact us if you have any "
#~ "question.\n"
#~ "                </p>\n"
#~ "            </div>\n"
#~ "        "
#~ msgstr ""
#~ "<div style=\"margin: 0px; padding: 0px;\">\n"
#~ "                <p style=\"box-sizing:border-box;margin: 0px; padding: "
#~ "0px; font-size: 13px;\">\n"
#~ "                    Sehr geehrte/r\n"
#~ "                    <t t-out=\"object.partner_id.name\" data-oe-t-"
#~ "inline=\"true\"></t>\n"
#~ "                    <t t-if=\"object.partner_id.parent_id\" data-oe-t-"
#~ "group-active=\"true\">\n"
#~ "                        <t t-out=\"object.partner_id.parent_id.name\" "
#~ "data-oe-t-inline=\"true\"></t>\n"
#~ "                    </t>\n"
#~ "                    <br>\n"
#~ "                    <br>\n"
#~ "                    Hiermit übermitteln wie Ihnen unseren RMA Auftrag\n"
#~ "                    <strong style=\"box-sizing:border-box;font-weight:500;"
#~ "\">\n"
#~ "                        <t t-out=\"object.name\" data-oe-t-"
#~ "inline=\"true\"></t>\n"
#~ "                    </strong>\n"
#~ "                    von\n"
#~ "                    <t t-out=\"object.company_id.name\" data-oe-t-"
#~ "inline=\"true\"></t>\n"
#~ "                    .\n"
#~ "                    <br>\n"
#~ "                    <br>\n"
#~ "                    Zögern Sie nicht uns zu kontaktieren.\n"
#~ "                </p>\n"
#~ "            </div>\n"
#~ "        "

#~ msgid ""
#~ "<div style=\"margin: 0px; padding: 0px;\">\n"
#~ "                <p style=\"margin: 0px; padding: 0px; font-size: 13px;"
#~ "\">\n"
#~ "                    Dear\n"
#~ "                    <t t-out=\"object.partner_id.name\"/>\n"
#~ "                    <t t-if=\"object.partner_id.parent_id\">\n"
#~ "                        <t t-out=\"object.partner_id.parent_id.name\"/>\n"
#~ "                    </t>\n"
#~ "                    <br/>\n"
#~ "                    <br/>\n"
#~ "                    The products for your RMA\n"
#~ "                    <strong>\n"
#~ "                        <t t-out=\"object.name\"/>\n"
#~ "                    </strong>\n"
#~ "                    from\n"
#~ "                    <t t-out=\"object.company_id.name\"/>\n"
#~ "                    have been received in our warehouse.\n"
#~ "                    <br/>\n"
#~ "                    <br/>\n"
#~ "                    Do not hesitate to contact us if you have any "
#~ "question.\n"
#~ "                </p>\n"
#~ "            </div>\n"
#~ "        "
#~ msgstr ""
#~ "<div style=\"margin: 0px; padding: 0px;\">\n"
#~ "                <p style=\"margin: 0px; padding: 0px; font-size: 13px;"
#~ "\">\n"
#~ "                    Sehr geehrte/r\n"
#~ "                    <t t-out=\"object.partner_id.name\"/>\n"
#~ "                    <t t-if=\"object.partner_id.parent_id\">\n"
#~ "                        <t t-out=\"object.partner_id.parent_id.name\"/>\n"
#~ "                    </t>\n"
#~ "                    <br/>\n"
#~ "                    <br/>\n"
#~ "Die Produkte für ihren RMA\n"
#~ "                    <strong>\n"
#~ "                        <t t-out=\"object.name\"/>\n"
#~ "                    </strong>\n"
#~ "                    von\n"
#~ "                    <t t-out=\"object.company_id.name\"/>\n"
#~ "wurden in unserem Lager erfolgreich entgegen genommen.\n"
#~ "                    <br/>\n"
#~ "                    <br/>\n"
#~ "                    Zögern Sie nicht uns bei weiteren Fragen erneut zu "
#~ "kontaktieren\n"
#~ "                </p>\n"
#~ "            </div>\n"
#~ "        "

#~ msgid ""
#~ "<div style=\"margin: 0px; padding: 0px;\">\n"
#~ "                <p style=\"margin: 0px; padding: 0px; font-size: 13px;"
#~ "\">\n"
#~ "                    Dear\n"
#~ "                    <t t-out=\"object.partner_id.name\"/>\n"
#~ "                    <t t-if=\"object.partner_id.parent_id\">\n"
#~ "                        <t t-out=\"object.partner_id.parent_id.name\"/>\n"
#~ "                    </t>\n"
#~ "                    <br/>\n"
#~ "                    <br/>\n"
#~ "                    You've succesfully placed your RMA\n"
#~ "                    <strong>\n"
#~ "                        <t t-out=\"object.name\"/>\n"
#~ "                    </strong>\n"
#~ "                    on\n"
#~ "                    <t t-out=\"object.company_id.name\"/>\n"
#~ "                    . Our team will check it and will validate it as soon "
#~ "as possible.\n"
#~ "                    <br/>\n"
#~ "                    <br/>\n"
#~ "                    Do not hesitate to contact us if you have any "
#~ "question.\n"
#~ "                </p>\n"
#~ "            </div>\n"
#~ "        "
#~ msgstr ""
#~ "<div style=\"margin: 0px; padding: 0px;\">\n"
#~ "                <p style=\"box-sizing:border-box;margin: 0px; padding: "
#~ "0px; font-size: 13px;\">\n"
#~ "                    Sehr geehrte/r\n"
#~ "                    <t t-out=\"object.partner_id.name\" data-oe-t-"
#~ "inline=\"true\"></t>\n"
#~ "                    <t t-if=\"object.partner_id.parent_id\" data-oe-t-"
#~ "group-active=\"true\">\n"
#~ "                        <t t-out=\"object.partner_id.parent_id.name\" "
#~ "data-oe-t-inline=\"true\"></t>\n"
#~ "                    </t>\n"
#~ "                    <br>\n"
#~ "                    <br>\n"
#~ "                    Sie haben erfolgreich ihren Reklamationsauftrag "
#~ "eingebracht.\n"
#~ "                    <strong style=\"box-sizing:border-box;font-weight:500;"
#~ "\">\n"
#~ "                        <t t-out=\"object.name\" data-oe-t-"
#~ "inline=\"true\"></t>\n"
#~ "                    </strong>\n"
#~ "                    für\n"
#~ "                    <t t-out=\"object.company_id.name\" data-oe-t-"
#~ "inline=\"true\"></t>\n"
#~ "                    . Unser Team wird die Anfrage bearbeiten uns sich so "
#~ "bald wie möglich bei Ihnen melden.\n"
#~ "                    <br>\n"
#~ "                    <br>\n"
#~ "Zögern Sie nicht uns bei weiteren Fragen erneut zu kontaktieren\n"
#~ "                </p>\n"
#~ "            </div>\n"
#~ "        "

#~ msgid "Number of messages which requires an action"
#~ msgstr "Anzahl der Nachrichten, die eine Aktion erfordern"

#~ msgid "Number of unread messages"
#~ msgstr "Anzahl der ungelesenen Nachrichten"

#~ msgid "Unread Messages"
#~ msgstr "ungelesene Nachrichten"

#~ msgid "Unread Messages Counter"
#~ msgstr "Zähler für ungelesene Nachrichten"

#~ msgid "Users"
#~ msgstr "Benutzer"
