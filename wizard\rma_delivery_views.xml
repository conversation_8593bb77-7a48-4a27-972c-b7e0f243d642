<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2020 Tecnativa - <PERSON>
     Copyright 2023 Tecnativa - Pedro <PERSON>ez<PERSON>
     License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl). -->
<odoo>
    <record id="rma_redelivery_wizard_view_form" model="ir.ui.view">
        <field name="name">rma.delivery.wizard.form</field>
        <field name="model">rma.delivery.wizard</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <group>
                        <field name="scheduled_date" />
                        <field name="warehouse_id" invisible="type != 'replace'" />
                        <field
                            name="rma_return_grouping"
                            invisible="type == 'replace' or rma_count == 1"
                        />
                    </group>
                    <group>
                        <field name="uom_category_id" invisible="1" />
                        <field
                            name="product_id"
                            invisible="type != 'replace' or rma_count > 1"
                            required="type == 'replace' and rma_count == 1"
                        />
                        <label for="product_uom_qty" invisible="rma_count > 1" />
                        <div class="o_row" invisible="rma_count > 1">
                            <field name="product_uom_qty" required="rma_count == 1" />
                            <field
                                name="product_uom"
                                groups="uom.group_uom"
                                required="rma_count == 1"
                                domain="[('category_id', '=', uom_category_id)]"
                            />
                            <field name="product_uom" invisible="1" />
                        </div>
                    </group>
                </group>
                <field name="rma_count" invisible="1" />
                <field name="type" invisible="1" />
                <footer>
                    <button
                        name="action_deliver"
                        string="Deliver"
                        type="object"
                        class="btn-primary"
                    />
                    <button string="Cancel" class="btn-secondary" special="cancel" />
                </footer>
            </form>
        </field>
    </record>

    <record id="rma_delivery_wizard_action" model="ir.actions.act_window">
        <field name="name">Return to customer</field>
        <field name="res_model">rma.delivery.wizard</field>
        <field name="view_mode">form</field>
        <field name="binding_model_id" ref="rma.model_rma" />
        <field name="binding_view_types">list</field>
        <field name="target">new</field>
        <field name="context">{'rma_delivery_type': 'return'}</field>
    </record>
</odoo>
