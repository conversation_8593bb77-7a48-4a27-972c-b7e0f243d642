<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="rma_tag_view_search" model="ir.ui.view">
        <field name="model">rma.tag</field>
        <field name="arch" type="xml">
            <search string="RMA Tags">
                <field name="name" />
                <filter
                    string="Archived"
                    name="inactive"
                    domain="[('active','=',False)]"
                />
                <filter
                    string="Active"
                    name="active"
                    domain="[('active','!=',False)]"
                />
            </search>
        </field>
    </record>
    <record id="view_rma_tag_form" model="ir.ui.view">
        <field name="name">Rma Tags</field>
        <field name="model">rma.tag</field>
        <field name="arch" type="xml">
            <form string="RMA Tag">
                <sheet>
                    <group>
                        <field name="name" />
                        <field name="is_public" />
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="view_rma_tag_list" model="ir.ui.view">
        <field name="name">RMA Tags</field>
        <field name="model">rma.tag</field>
        <field eval="6" name="priority" />
        <field name="arch" type="xml">
            <tree>
                <field name="name" />
                <field name="is_public" />
                <field name="active" />
            </tree>
        </field>
    </record>
    <record id="action_rma_tag" model="ir.actions.act_window">
        <field name="name">RMA Tags</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">rma.tag</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
            Create a new RMA tag
            </p>
            <p>
            Manage RMA tags to better classify them for tracking and analysis purposes.
            </p>
        </field>
    </record>
    <menuitem
        id="rma_configuration_rma_tag_menu"
        name="RMA Tags"
        parent="rma_configuration_menu"
        action="action_rma_tag"
    />
</odoo>
