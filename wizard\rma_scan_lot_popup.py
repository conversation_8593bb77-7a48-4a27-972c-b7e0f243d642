from odoo import models, fields, api, _
from odoo.exceptions import UserError

class RmaScanLotPopup(models.TransientModel):
    _name = 'rma.scan.lot.popup'
    _description = 'Popup Saisie Lot inconnu RMA'

    rma_id = fields.Many2one('rma', required=True)
    lot_name = fields.Char('Numéro de série', required=True)
    product_id = fields.Many2one('product.product', required=True, string="Produit")
    reason_id = fields.Many2one('rma.reason', string="Raison du retour")
    comment = fields.Text("Commentaire (optionnel)")

    def action_create_lot_line(self):
        self.ensure_one()
        lot = self.env['stock.lot'].create({'name': self.lot_name, 'product_id': self.product_id.id})
        self.env['rma.line'].create({
            'rma_id': self.rma_id.id,
            'product_id': self.product_id.id,
            'lot_id': lot.id,
            'reason_id': self.reason_id.id if self.reason_id else False,
            'description': self.comment,
        })
        return {'type': 'ir.actions.act_window_close'} 