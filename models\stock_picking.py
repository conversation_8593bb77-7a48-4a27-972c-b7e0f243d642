# Copyright 2020 Tecnativa - <PERSON>
# License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl).
from odoo.exceptions import ValidationError
from odoo import fields, models, _
from odoo import Command
from collections import defaultdict


class RepairOrder(models.Model):
    _inherit = "repair.order"

    tracking_uuid = fields.Char('Tracking UUID')
    order_general_id = fields.Many2one('sale.order')
    comment = fields.Text('Commentaire')
    reason_id = fields.Many2many('rma.reason', 'reason_relation_repair_order_line', 'Raison')

    def _create_grouped_sale_order_lines2(self, sale_order):
        # Dictionnaire clé = (product_id, product_uom_id), valeur = dict avec qty, name, price
        grouped_lines = defaultdict(lambda: {
            'product_uom_qty': 0.0,
            'price_unit': 0.0,
            'name': '',
        })

        for move in self.mapped('move_ids'):
            if move.product_id and move.product_uom:
                key = (move.product_id.id, move.product_uom.id)
                grouped_lines[key]['product_uom_qty'] += move.product_uom_qty
                grouped_lines[key]['price_unit'] = move.price_unit or move.product_id.lst_price
                grouped_lines[key]['name'] = move.name or move.product_id.display_name

        for (product_id, uom_id), values in grouped_lines.items():
            self.env['sale.order.line'].create({
                'order_id': sale_order.id,
                'product_id': product_id,
                'product_uom_qty': values['product_uom_qty'],
                'product_uom': uom_id,
                'price_unit': values['price_unit'],
                'name': values['name'],
            })

    def _create_grouped_sale_order_lines(self, sale_order):
        for move in self.mapped('move_ids'):
            if move.product_id:
                self.env['sale.order.line'].create({
                    'order_id': sale_order.id,
                    'product_id': move.product_id.id,
                    'product_uom_qty': move.product_uom_qty,
                    'product_uom': move.product_uom.id,
                    'price_unit': move.price_unit or move.product_id.lst_price,
                    'name': move.name or move.product_id.display_name,
                })

    def createSaleOrder(self):
        partner_id = None
        picking_type_id = None
        company_id = None

        for rec in self:
            # check state
            if rec.state != 'done':
                raise ValidationError(_("Impossible de créer un devis pour un ordre de réparation non achevé !"))
            if not partner_id:
                partner_id = rec.partner_id.id
            if rec.partner_id.id != partner_id:
                raise ValidationError(_("Vous ne pouvez pas créer un devis avec plusieurs clients !"))

            if rec.sale_order_id or rec.order_general_id:
                raise ValidationError(
                    _("Vous ne pouvez pas créer un devis pour un ordre de réparation déjà associé à un devis !"))

            if not picking_type_id:
                picking_type_id = rec.picking_type_id
            elif rec.picking_type_id != picking_type_id:
                raise ValidationError(_("Tous les ordres doivent avoir le même type de picking."))

            if not company_id:
                company_id = rec.company_id
            elif rec.company_id != company_id:
                raise ValidationError(_("Tous les ordres doivent appartenir à la même société."))

        # Créer le devis
        sale_order_vals = {
            'partner_id': partner_id,
            'company_id': company_id.id,
            'warehouse_id': picking_type_id.warehouse_id.id,
            'repair_order_ids': [Command.link(rec.id) for rec in self],
        }
        order = self.env['sale.order'].sudo().create(sale_order_vals)

        # Lier le devis à chaque ordre de réparation
        for rec in self:
            rec.order_general_id = order.id

        # Créer les lignes de vente groupées
        self._create_grouped_sale_order_lines2(order)

        return {
            'type': 'ir.actions.act_window',
            'name': _('Quotation'),
            'res_model': 'sale.order',
            'view_mode': 'form',
            'res_id': order.id,
            'target': 'current',
        }

    def action_view_sale_order(self):
        res = super(RepairOrder, self).action_view_sale_order()
        if not self.order_general_id: return res
        return {
            'type': 'ir.actions.act_window',
            'name': 'Sale Order',
            'res_model': 'sale.order',
            'view_mode': 'form',
            'res_id': self.order_general_id.id,
            'view_id': self.env.ref('sale.view_order_form').id,
            'target': 'current',
        }

class StockPicking(models.Model):
    _inherit = "stock.picking"

    is_rma = fields.Boolean(default=False)
    rma_id = fields.Many2one('rma')
    rma_count = fields.Integer(
        string="RMA count",
        compute="_compute_rma_count",
    )

    state_repair = fields.Selection(
        [
            ("none", "Nouveau"),
            ("received", "Reçu"),
            ("on_repair", "En réparation"),
            ("finished", "Terminé")
        ],
        compute='checkIfAllProductIsRepari',
    )

    def checkIfAllProductIsRepari(self):
        for rec in self:
            rec.state_repair = 'none'

    def _compute_rma_count(self):
        for rec in self:
            rec.rma_count = len(rec.move_ids.mapped("rma_ids"))

    def openRmaViweForm(self):
        action = self.env['ir.actions.act_window']._for_xml_id(
            'rma.action_open_rma_view_form_on_pickings')
        action['res_id'] = self.rma_id.id

        return action

    def openViewSerialNumber(self):
        action = self.env['ir.actions.act_window']._for_xml_id(
            'rma.open_wizard_input_serial_number_action')
        action['res_id'] = self.id
        return action


    def openListProductsToRepair(self):
        action = self.env['ir.actions.act_window']._for_xml_id(
            'rma.action_open_stock_move_line_tree2')
        action['domain'] = [('picking_id', '=', self.id)]

        return action

    # def copy(self, default=None):
    #     self.ensure_one()
    #     if self.env.context.get("set_rma_picking_type"):
    #         location_dest_id = default.get("location_dest_id")
    #         if location_dest_id:
    #             warehouse = self.env["stock.warehouse"].search(
    #                 [("rma_loc_id", "parent_of", location_dest_id)], limit=1
    #             )
    #             if warehouse:
    #                 default["picking_type_id"] = warehouse.rma_in_type_id.id
    #     return super().copy(default)

    def action_view_rma(self):
        self.ensure_one()
        action = self.env["ir.actions.act_window"]._for_xml_id("rma.rma_action")
        rma = self.move_ids.rma_ids
        if len(rma) == 1:
            action.update(
                res_id=rma.id,
                view_mode="form",
                view_id=False,
                views=False,
            )
        else:
            action["domain"] = [("id", "in", rma.ids)]
        return action
