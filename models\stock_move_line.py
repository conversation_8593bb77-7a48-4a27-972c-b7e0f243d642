from odoo import fields, models, api


class StockMoveLine(models.Model):
    _inherit = 'stock.move.line'

    comment = fields.Text('Commentaire')
    reason_id = fields.Many2many('rma.reason','reason_relation_stock_move_line', 'Raison')
    is_rma = fields.Boolean(default=False)
    tracking_uuid = fields.Char('Tracking UUID')

    state_repair = fields.Selection(
        [
            ("none", "Nouveau"),
            ("received", "Reçu"),
            ("on_repair", "En réparation"),
            ("finished", "Terminé")
        ],
        compute='checkIfAllProductIsRepari',
    )

    def checkIfAllProductIsRepari(self):
        for rec in self:
            rec.state_repair = 'none'


class StockMoveLine(models.Model):
    _inherit = 'stock.move'

    comment = fields.Text('Commentaire')
    reason_id = fields.Many2many('rma.reason','stock_move_relation_reason', 'Raison')
    tracking = fields.Selection(related="product_id.tracking")
    is_rma = fields.<PERSON><PERSON><PERSON>(default=False)


    def openViewSerialNumber(self):
        action = self.env['ir.actions.act_window']._for_xml_id(
            'rma.open_wizard_stock_picking_action')
        action['res_id'] = self.id
        return action

class RmaStockMoveLine(models.Model):
    _name = 'rma.stock.move.line'
    _rec_name = 'product_id'

    product_id = fields.Many2one('product.product', 'Produit')
    num_serial = fields.Char('Numéro de série')
    scheduled_date = fields.Datetime('Date prévue', store=True)
    date_deadline = fields.Date('Date d\'échéance', index=True, required=True, default=fields.Date.context_today)
    is_rma = fields.Boolean(default=False)
    picking_id = fields.Many2one('stock.picking', 'Bon de réception')
    comment = fields.Text('Commentaire')
    reason_id = fields.Many2many('rma.reason','reason_relation_rma_stock_move_line', 'Raison')
    partner_id = fields.Many2one('res.partner', 'Client')
    state = fields.Selection([('on_hold', 'En attente'), ('on_repair', 'En réparation')], string='Status', default='on_hold')
    lot_id = fields.Many2one('stock.lot')
    order_id = fields.Many2one('repair.order')
    tracking_uuid = fields.Char('Tracking UUID')
    tracking = fields.Selection(related="product_id.tracking")

    def openPickingViewForm(self):
        action = self.env["ir.actions.act_window"]._for_xml_id("rma.action_picking_tree_incoming")
        action['res_id'] = self.picking_id.id
        action['view_mode'] = 'form'
        action['target'] = 'current'
        return action

    def openOrderRepairViewForm(self):
        # return True
        action = self.env["ir.actions.act_window"]._for_xml_id("rma.action_repair_order_tree")
        action['res_id'] = self.order_id.id
        action['target'] = 'current'
        return action



    def makeStateOnHold(self):
        self.state = 'on_hold'

    def createOrderRepair(self):
        # repair.order
        repairOrder = self.env['repair.order']
        res = repairOrder.sudo().create({
            'partner_id': self.partner_id.id,
            'product_id': self.product_id.id,
            'product_qty': 1,
            'schedule_date': self.scheduled_date,
            'state': 'draft',
            'picking_id': self.picking_id.id,
            'lot_id': self.lot_id.id,
            'reason_id': [(6, 0, self.reason_id.ids)],
            'comment': self.comment,
            'tracking_uuid': self.tracking_uuid,
        })
        if res:
            self.order_id = res.id
            print(self.order_id.name)
            self.state = 'on_repair'
            action = self.env['ir.actions.act_window']._for_xml_id(
                'repair.action_repair_order_tree')
            action["res_id"] = res.id
            action["views"] = [(self.env.ref('repair.view_repair_order_form').id, 'form')]
            action["view_mode"] = 'form'
            return action

class StockPicking(models.Model):
    _inherit = 'stock.picking'


    def button_validate(self):
        # creer les lines dans rma reparation
        if self.is_rma:
            # rmStock = self.env['rma.stock.move.line']
            rmStock = self.env['repair.order']
            for rec in self.move_ids:
                for move_id in rec.move_line_ids:
                    # creer les lignes dans rma reparation
                    rmStock.sudo().create({
                        'product_id': move_id.product_id.id,
                        'schedule_date': rec.picking_id.scheduled_date,
                        'comment': move_id.comment,
                        'tracking_uuid': move_id.tracking_uuid if move_id.tracking_uuid else rec.tracking_uuid,
                        'reason_id': move_id.reason_id.ids,
                        'picking_id': rec.picking_id.id,
                        'partner_id': rec.partner_id.id,
                        'lot_id': move_id.lot_id.id,
                    })
                    print(move_id.product_id.name)
        # move_line_ids
        res = super(StockPicking, self).button_validate()
        return res
