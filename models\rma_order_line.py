from email.policy import default

from odoo import fields, api, models
import uuid


class RmaOrderLine(models.Model):
    _name = "rma.order.line"


    product_id = fields.Many2one('product.product', 'Produit')
    quantity = fields.Integer('Quantité', default=1)
    comment = fields.Text('Commentaire')
    reason_id = fields.Many2many('rma.reason','rma_reasion_relation_id', 'Raison')
    rma_id = fields.Many2one('rma')
    serail_number = fields.One2many('stock.lot', 'rma_line_id')
    tracking = fields.Selection(related='product_id.product_tmpl_id.tracking')
    tracking_uuid = fields.Char('Tracking UUID')
    state_repair = fields.Selection(
        [
            ("none", "Brouillon"),
            ("on_hold", "En attente de réception"),
            ("cancel", "Annuler"),
            ("received", "Reçu"),
            ("on_repair", "En réparation"),
            ("finished", "Terminé")
        ],
        default='none',
        compute="calculateStateRepair"
    )

    @api.model
    def create(self, vals):
        vals['tracking_uuid'] = str(uuid.uuid4())
        print("tracking_uuid",  vals['tracking_uuid'])
        return super().create(vals)


    @api.depends('rma_id.receipt_id.state')
    def calculateStateRepair(self):
        for rec in self:
            if not rec.rma_id.receipt_id:
                rec.state_repair = 'none'
            elif rec.rma_id.receipt_id.state == 'cancel':
                rec.state_repair = 'cancel'
            elif rec.rma_id.receipt_id.state in ('draft', 'waiting'):
                rec.state_repair = 'none'
            else:
                rec.state_repair = 'received'

    @api.onchange('serail_number')
    def computeQuantity(self):
        for rec in self:
            lenghtSerial = len(rec.serail_number)
            rec.quantity = lenghtSerial

    def valide_serial_number(self):
        lenghtSerial = len(self.serail_number)
        self.quantity = lenghtSerial if lenghtSerial > 0 else 1

    def openViewSerialNumber(self):
        action = self.env['ir.actions.act_window']._for_xml_id(
            'rma.open_wizard_input_serial_number_action')
        action['res_id'] = self.id
        return action

class RmaOrderLine(models.Model):
    _name = "serial.product.line"
    _rec_name = 'name'

    rma_line_id = fields.Many2one('rma.order.line', 'Produit')
    name = fields.Char('Lot/Numero de serie')

class RmaStockOrderLine(models.Model):
    _inherit = "stock.lot"

    rma_line_id = fields.Many2one('rma.order.line', 'Produit')
    comment = fields.Text('Commentaire')
    reason_id = fields.Many2many('rma.reason','reason_rma_order_line', 'Raison')
    state_repair = fields.Selection(
        [
            ("none", "Brouillon"),
            ("received", "Reçu"),
            ("on_repair", "En réparation"),
            ("finished", "Terminé")
        ],
        default='none'
    )
    tracking_uuid = fields.Char('Tracking UUID')


    @api.model
    def create(self, vals):
        vals['tracking_uuid'] = str(uuid.uuid4())
        print("tracking_uuid",  vals['tracking_uuid'])
        return super().create(vals)
