<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2020 Tecnativa - <PERSON>
     License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl). -->
<odoo>
    <!-- Application -->
    <record id="rma_module_category" model="ir.module.category">
        <field name="name">RMA</field>
        <field
            name="description"
        >Manage Return Merchandise Authorizations (RMAs).</field>
    </record>
    <!-- Access Groups -->
    <record id="rma_group_user_own" model="res.groups">
        <field name="name">User: Own Documents Only</field>
        <field name="category_id" ref="rma_module_category" />
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]" />
        <field
            name="comment"
        >the user will have access to his own data in the RMA application.</field>
    </record>
    <record id="rma_group_user_all" model="res.groups">
        <field name="name">User: All Documents</field>
        <field name="category_id" ref="rma_module_category" />
        <field name="implied_ids" eval="[(4, ref('rma_group_user_own'))]" />
        <field
            name="comment"
        >the user will have access to all records of everyone in the RMA application.</field>
    </record>
    <record id="rma_group_manager" model="res.groups">
        <field name="name">Manager</field>
        <field
            name="comment"
        >the user will have an access to the RMA configuration as well as statistic reports.</field>
        <field name="category_id" ref="rma_module_category" />
        <field name="implied_ids" eval="[(4, ref('rma_group_user_all'))]" />
        <field
            name="users"
            eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"
        />
    </record>
    <record id="group_rma_manual_finalization" model="res.groups">
        <field name="name">Allow RMA manual finalization</field>
        <field name="category_id" ref="base.module_category_hidden" />
    </record>
    <!-- Record Rules -->
    <record id="rma_rule_user_own" model="ir.rule">
        <field name="name">Personal RMAs</field>
        <field name="model_id" ref="model_rma" />
        <field
            name="domain_force"
        >['|',('user_id','=',user.id),('user_id','=',False)]</field>
        <field name="groups" eval="[(4, ref('rma_group_user_own'))]" />
    </record>
    <record id="rma_rule_user_all" model="ir.rule">
        <field name="name">All RMAs</field>
        <field name="model_id" ref="model_rma" />
        <field name="domain_force">[(1,'=',1)]</field>
        <field name="groups" eval="[(4, ref('rma_group_user_all'))]" />
    </record>
    <!-- RMA model rules for portal users -->
    <record id="rma_rule_portal" model="ir.rule">
        <field name="name">RMA portal users</field>
        <field name="model_id" ref="rma.model_rma" />
        <field
            name="domain_force"
        >[('message_partner_ids', 'child_of', [user.partner_id.commercial_partner_id.id])]</field>
        <field name="groups" eval="[(4, ref('base.group_portal'))]" />
    </record>
    <!-- Multi-Company Rules -->
    <record id="rma_rule_multi_company" model="ir.rule">
        <field name="name">RMA multi-company</field>
        <field name="model_id" ref="model_rma" />
        <field name="global" eval="True" />
        <field
            name="domain_force"
        >['|',('company_id','=',False),('company_id','in',company_ids)]</field>
    </record>
    <record id="rma_team_rule_multi_company" model="ir.rule">
        <field name="name">RMA team multi-company</field>
        <field name="model_id" ref="model_rma_team" />
        <field name="global" eval="True" />
        <field
            name="domain_force"
        >['|',('company_id','=',False),('company_id','in',company_ids)]</field>
    </record>
    <record id="rma_finalization_comp_rule" model="ir.rule">
        <field name="name">RMA Finalization Reason multi-company</field>
        <field name="model_id" ref="model_rma_finalization" />
        <field name="global" eval="True" />
        <field
            name="domain_force"
        > ['|', ('company_id', 'in', company_ids), ('company_id', '=', False)]</field>
    </record>
    <!-- Allow to refund RMAs -->
    <record id="rma_account_move_personal_rule" model="ir.rule">
        <field name="name">RMA Personal Invoice</field>
        <field ref="model_account_move" name="model_id" />
        <field
            name="domain_force"
        >[('move_type', '=', 'out_refund'), '|', ('invoice_user_id', '=', user.id), ('invoice_user_id', '=', False)]</field>
        <field name="groups" eval="[(4, ref('rma.rma_group_user_own'))]" />
    </record>
    <record id="rma_account_move_line_personal_rule" model="ir.rule">
        <field name="name">RMA Personal Invoice Lines</field>
        <field ref="model_account_move_line" name="model_id" />
        <field
            name="domain_force"
        >[('move_id.move_type', '=', 'out_refund'), '|', ('move_id.invoice_user_id', '=', user.id), ('move_id.invoice_user_id', '=', False)]</field>
        <field name="groups" eval="[(4, ref('rma.rma_group_user_own'))]" />
    </record>
    <!-- New users will belong to rma_group_user_own  -->
    <record id="base.default_user" model="res.users">
        <field name="groups_id" eval="[(4, ref('rma_group_user_own'))]" />
    </record>
</odoo>
