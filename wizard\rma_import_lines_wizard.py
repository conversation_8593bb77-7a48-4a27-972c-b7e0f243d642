import base64
import csv
from io import StringIO
from odoo import models, fields, _
from odoo.exceptions import UserError

class RmaImportLinesWizard(models.TransientModel):
    _name = 'rma.import.lines.wizard'
    _description = 'Import Lignes RMA (CSV/Excel)'

    rma_id = fields.Many2one('rma', required=True)
    file = fields.Binary('Fichier CSV', required=True)
    filename = fields.Char('Nom du fichier')

    def action_import_lines(self):
        self.ensure_one()
        if not self.file:
            raise UserError(_('Veuillez sélectionner un fichier à importer.'))

        data = base64.b64decode(self.file)
        file_content = data.decode('utf-8')
        reader = csv.DictReader(StringIO(file_content))
        created = 0

        for row in reader:
            product_code = row.get('product_code')
            lot_name = row.get('lot_name')
            reason_code = row.get('reason_code')
            comment = row.get('comment', '')

            product = self.env['product.product'].search([('default_code', '=', product_code)], limit=1)
            if not product:
                raise UserError(_("Produit non trouvé : %s") % product_code)

            lot = self.env['stock.lot'].search([('name', '=', lot_name), ('product_id', '=', product.id)], limit=1)
            if not lot:
                lot = self.env['stock.lot'].create({'name': lot_name, 'product_id': product.id})

            reason = self.env['rma.reason'].search([('code', '=', reason_code)], limit=1)
            if not reason or not reason.exists():
                reason = False

            self.env['rma.line'].create({
                'rma_id': self.rma_id.id,
                'product_id': product.id,
                'lot_id': lot.id,
                'reason_id': reason.id if reason else False,
                'description': comment,
            })
            created += 1

        return {
            'type': 'ir.actions.act_window_close',
            'tag': 'reload',
            'message': _('%s lignes importées avec succès !') % created,
        }
