<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <record id="view_repair_order_form_inherit" model="ir.ui.view">
            <field name="name">repair.order.form.inherit</field>
            <field name="model">repair.order</field>
            <field name="inherit_id" ref="repair.view_repair_order_form"/>
            <field name="arch" type="xml">

<!--                <button name="action_view_sale_order" type="object" string="Commande client" icon="fa-dollar" class="oe_stat_button" invisible="not sale_order_id">-->
<!--                                <div class="o_field_widget o_stat_info">-->
<!--                                    <span class="o_stat_value">-->
<!--                                        <field name="sale_order_id" widget="statinfo" nolabel="1" class="mr4" field_id="sale_order_id_0"/>-->
<!--                                    </span>-->
<!--                                    <span class="o_stat_text">Bon de commande</span>-->
<!--                                </div>-->
<!--                            </button>-->

                <xpath expr="//field[@name='tag_ids']" position="after">
                    <field name="order_general_id" invisible="1"/>
                </xpath>
                <xpath expr="//button[@name='action_view_sale_order']" position="attributes">
                    <attribute name="invisible">not sale_order_id and not order_general_id</attribute>
                </xpath>
                <xpath expr="//button[@name='action_create_sale_order']" position="replace">
                    <button name="action_create_sale_order" type="object" string="Créer le devis"
                            invisible="state == 'cancel' or sale_order_id or order_general_id"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
