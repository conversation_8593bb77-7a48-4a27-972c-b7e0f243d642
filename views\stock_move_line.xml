<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <record id="view_move_line_form" model="ir.ui.view">
            <field name="name">stock.move.operations.form</field>
            <field name="model">stock.move</field>
            <field name="priority">1000</field>
            <field name="arch" type="xml">
                <form string="Move Detail">
                    <h1>Hello world</h1>
                    <field name="sequence" invisible="1"/>
                    <field name="company_id" invisible="1"/>
                    <field name="state" invisible="1"/>
                    <field name="location_id" invisible="1"/>
                    <field name="location_dest_id" invisible="1"/>
                    <field name="picking_id" invisible="1" readonly="state == 'done'"/>
                    <field name="picking_type_id" invisible="1"/>
                    <field name="is_locked" invisible="1"/>
                    <field name="picking_type_entire_packs" invisible="1"/>
                    <field name="display_assign_serial" invisible="1"/>
                    <field name="display_import_lot" invisible="1"/>
                    <field name="product_uom_category_id" invisible="1"/>
                    <field name="product_uom" invisible="1" groups="!uom.group_uom"/>
                    <field name="picking_code" invisible="1"/>
                    <field name="has_tracking" invisible="1"/>
                    <field name="show_reserved" invisible="1"/>
                    <field name="show_quant" invisible="1"/>
                    <field name="show_lots_text" invisible="1"/>
                    <field name="show_lots_m2o" invisible="1"/>
                    <field name="quantity" invisible="1"/>
                    <group>
                        <group>
                            <field name="product_id" readonly="id or move_line_ids"/>
                            <label for="product_uom_qty"/>
                            <div class="o_row">
                                <span>
                                    <field name="product_uom_qty" readonly="state != 'draft'" nolabel="1"/>
                                </span>
                                <span>
                                    <field name="product_uom" groups="uom.group_uom" readonly="state != 'draft'"
                                           nolabel="1" options="{'no_open': True}"/>
                                </span>
                            </div>
                        </group>
                        <group/>
                    </group>
                    <field name="move_line_ids"
                           readonly="0"
                           context="{'tree_view_ref': 'stock.view_stock_move_line_operation_tree', 'form_view_ref': 'stock.view_move_line_mobile_form', 'default_picking_id': picking_id, 'default_move_id': id, 'default_product_id': product_id, 'default_location_id': location_id, 'default_location_dest_id': location_dest_id, 'default_company_id': company_id, 'active_picking_id': picking_id}"
                           widget="sml_x2_many">
                        <tree editable="bottom">
                            <field name="company_id" column_invisible="True"/>
                            <field name="state" column_invisible="True"/>
                            <field name="tracking" column_invisible="True"/>
                            <field name="product_uom_id" column_invisible="True"/>
                            <field name="product_uom_category_id" column_invisible="True"/>
                            <field name="picking_id" column_invisible="True"/>
                            <field name="move_id" column_invisible="True"/>
                            <field name="location_id" column_invisible="True"/>
                            <field name="location_dest_id" column_invisible="True"/>
                            <field name="product_id" readonly="1" force_save="1"/>
                            <field name="quantity"/>
                            <field name="lot_id" column_invisible="parent.has_tracking not in ('serial', 'lot')"
                                   required="tracking in ('serial', 'lot')"
                                   context="{'default_product_id': product_id, 'default_company_id': company_id}"
                                   groups="stock.group_production_lot"/>
                        </tree>
                    </field>
                </form>
            </field>
        </record>
    </data>
</odoo>
