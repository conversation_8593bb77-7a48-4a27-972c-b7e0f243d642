<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2020 Tecnativa - <PERSON>
     License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl). -->
<odoo>
    <record id="view_picking_form" model="ir.ui.view">
        <field name="name">stock.picking.form</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <div name="button_box">
                <style>
                    .fa-list{
                    display: none !important;
                    }
                </style>
                <field name="is_rma" invisible="1"/>
                <button
                    name="openRmaViweForm"
                    type="object"
                    class="oe_stat_button"
                    icon="fa-reply"
                    invisible="not is_rma"
                    groups="rma.rma_group_user_own"
                >
                    RMA
                </button>
                <!--                <button-->
                <!--                    nasme="openListProductsToRepair"-->
                <!--                    type="object"-->
                <!--                    class="oe_stat_button"-->
                <!--                    icon="fa-folder-open"-->
                <!--                    invisible="not is_rma and state != 'done'"-->
                <!--                    groups="rma.rma_group_user_own"-->
                <!--                >-->
                <!--                    Réparations-->
                <!--                </button>-->
            </div>


            <!--page operation-->
            <page name="operations" position="replace">
                <page string="Opérations" name="operations">
                    <field name="move_ids_without_package" mode="tree,kanban" widget="stock_move_one2many"
                           readonly="state == 'done' and is_locked"
                           context="{'default_company_id': company_id, 'default_date': scheduled_date, 'default_date_deadline': date_deadline, 'picking_type_code': picking_type_code, 'default_picking_id': id, 'form_view_ref': 'stock.view_stock_move_operations', 'address_in_id': partner_id, 'default_picking_type_id': picking_type_id, 'default_location_id': location_id, 'default_location_dest_id': location_dest_id, 'default_partner_id': partner_id}"
                           add-label="Ajouter un produit" field_id="move_ids_without_package_0">
                        <tree
                            decoration-muted="scrapped == True or state == 'cancel' or (state == 'done' and is_locked == True)"
                            string="Mouvements de stock" editable="1">
                            <field name="company_id" column_invisible="True" can_create="True" can_write="True"/>
                            <field name="name" column_invisible="True"/>
                            <field name="state" readonly="0" column_invisible="True" on_change="1"/>
                            <field name="picking_type_id" column_invisible="True" on_change="1" can_create="True"
                                   can_write="True"/>
                            <field name="move_line_ids" column_invisible="False" on_change="1"/>
                            <field name="location_id" column_invisible="True" on_change="1" can_create="True"
                                   can_write="True"/>
                            <field name="location_dest_id" column_invisible="True" on_change="1" can_create="True"
                                   can_write="True"/>
                            <field name="partner_id" column_invisible="True" readonly="state == 'done'"
                                   can_create="True"
                                   can_write="True"/>
                            <field name="scrapped" column_invisible="True"/>
                            <field name="picking_code" column_invisible="True" on_change="1"/>
                            <field name="product_type" column_invisible="True"/>
                            <field name="show_details_visible" column_invisible="True"/>
                            <field name="show_reserved" column_invisible="True"/>
                            <field name="additional" column_invisible="True"/>
                            <field name="move_lines_count" column_invisible="True"/>
                            <field name="is_locked" column_invisible="True"/>
                            <field name="product_uom_category_id" column_invisible="True"/>
                            <field name="has_tracking" column_invisible="True" on_change="1"/>
                            <field name="display_assign_serial" column_invisible="True"/>
                            <field name="product_id" context="{'default_detailed_type': 'product'}" required="1"
                                   readonly="(state != 'draft' and not additional) or move_lines_count &gt; 0"
                                   force_save="1" on_change="1" can_create="True" can_write="True"/>
                            <field name="restrict_lot_id" readonly="1" optional="show"/>
                            <field name="description_picking" string="Description" optional="hide"/>
                            <field name="description_bom_line" optional="show" column_invisible="not parent.has_kits"/>
                            <field name="date" optional="hide" on_change="1"/>
                            <field name="date_deadline" optional="hide" on_change="1"/>
                            <field name="is_quantity_done_editable" column_invisible="True"/>
                            <field name="show_quant" column_invisible="True"/>
                            <field name="show_lots_text" column_invisible="True"/>
                            <field name="show_lots_m2o" column_invisible="True"/>
                            <field name="display_assign_serial" column_invisible="True"/>
                            <field name="is_initial_demand_editable" column_invisible="True"/>
                            <field name="display_import_lot" column_invisible="True"/>
                            <field name="picking_type_entire_packs" column_invisible="True" on_change="1"/>
                            <field name="product_uom_qty" string="Demande" readonly="not is_initial_demand_editable"
                                   on_change="1"/>
                            <field name="forecast_expected_date" column_invisible="True" on_change="1"/>
                            <field name="forecast_availability" string="Prévision" optional="hide"
                                   column_invisible="parent.state in ('draft', 'done') or parent.picking_type_code != 'outgoing'"
                                   widget="forecast_widget" on_change="1"/>
                            <field name="product_qty" readonly="1" column_invisible="True" on_change="1"/>
                            <field name="quantity" string="Quantité" readonly="not is_quantity_done_editable"
                                   column_invisible="parent.state=='draft'"
                                   decoration-danger="product_uom_qty and quantity &gt; product_uom_qty and parent.state not in ['done', 'cancel']"
                                   on_change="1"/>
                            <field name="product_uom" readonly="state != 'draft' and not additional"
                                   options="{'no_open': True, 'no_create': True}" string="Unité" on_change="1"
                                   can_create="True" can_write="True"/>
                            <!--add the comment and the reason-->
                            <field name="tracking" column_invisible="1"/>
                            <field name="reason_id" widget="many2many_tags" optional="show"
                                   invisible="tracking == 'serial'"/>
                            <field name="comment" optional="show" invisible="tracking == 'serial'"/>
                            <field name="picked" optional="hide" column_invisible="parent.state=='draft'"
                                   on_change="1"/>
                            <field name="lot_ids" widget="many2many_tags" column_invisible="parent.state == 'draft'"
                                   invisible="not show_details_visible or has_tracking != 'serial'" optional="hide"
                                   options="{'create': [('parent.use_create_lots', '=', True)]}"
                                   context="{'default_company_id': company_id, 'default_product_id': product_id, 'active_picking_id': parent.id}"
                                   domain="[('product_id','=',product_id)]" on_change="1" can_create="True"
                                   can_write="True"/>
                            <field name="state_repair" widget="selection" string="Etat du réparations"/>
                            <field name="tracking_uuid"/>
                            <button name="action_assign_serial" type="object" icon="fa-plus-square" role="img"
                                    title="Assigner des numéros de série" invisible="not display_assign_serial"/>
                            <button name="openViewSerialNumber" type="object" icon="fa-list-ul" role="img"
                                    title="Assigner des numéros de série"
                                    invisible="tracking != 'serial'"
                            />
                            <button type="object" name="action_product_forecast_report" title="Rapport de prévision"
                                    icon="fa-area-chart"
                                    invisible="quantity == 0 and forecast_availability &lt;= 0 or (parent.picking_type_code == 'outgoing' and state != 'draft')"/>
                            <button type="object" name="action_product_forecast_report" title="Rapport de prévision"
                                    icon="fa-area-chart text-danger"
                                    invisible="quantity &gt; 0 or forecast_availability &gt; 0 or (parent.picking_type_code == 'outgoing' and state != 'draft')"/>
                        </tree>
                    </field>
                    <field name="id" invisible="1" field_id="id_0"/>
                    <field name="package_level_ids"
                           context="{'default_location_id': location_id, 'default_location_dest_id': location_dest_id, 'default_company_id': company_id}"
                           invisible="not picking_type_entire_packs" readonly="state == 'done'"
                           field_id="package_level_ids_0">
                        <tree editable="bottom" decoration-muted="state == 'done'">
                            <field name="is_fresh_package" column_invisible="True" on_change="1"/>
                            <field name="company_id" column_invisible="True" can_create="True" can_write="True"/>
                            <field name="package_id" readonly="state in ('confirmed', 'assigned', 'done', 'cancel')"
                                   options="{'no_create': True}" on_change="1" can_create="True" can_write="True"/>
                            <field name="state" on_change="1"/>
                            <field name="is_done"
                                   readonly="parent.state in ('draft', 'new', 'done') or is_fresh_package"/>
                            <button name="action_show_package_details" title="Montrer le contenu du colis" type="object"
                                    icon="fa-list"/>
                        </tree>
                    </field>
                </page>
            </page>
        </field>
    </record>


    <!--action open rma view form from pickings-->
    <record id="action_open_rma_view_form_on_pickings" model="ir.actions.act_window">
        <field name="name">RMA</field>
        <field name="res_model">rma</field>
        <field name="view_mode">form</field>
        <field name="target">current</field>
    </record>


    <!--action open picking stock-->
    <record id="action_picking_tree_incoming" model="ir.actions.act_window">
        <field name="name">Receipts</field>
        <field name="res_model">stock.picking</field>
        <field name="view_mode">form</field>
        <field name="target">current</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No receipt found. Let's create one!
            </p>
            <p>
                Receipts allow you to get products from a partner.
            </p>
        </field>
    </record>


    <!--action open view form order repair-->
    <record id="action_repair_order_tree" model="ir.actions.act_window">
        <field name="name">Repair Orders</field>
        <field name="res_model">repair.order</field>
        <field name="view_mode">form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No repair order found. Let's create one!
            </p>
            <p>
                In a repair order, you can detail the components you remove,
                add or replace and record the time you spent on the different
                operations.
            </p>
        </field>
    </record>


    <!--view form-->
    <record id="view_repair_rma_stock_order_form" model="ir.ui.view">
        <field name="name">repair.tag.form</field>
        <field name="model">rma.stock.move.line</field>
        <field name="arch" type="xml">
            <form string="Repair Tags">
                <header>
                    <button string="Remettre en attente"
                            name="makeStateOnHold"
                            type="object"
                            invisible="state == 'on_hold'"
                            data-hotkey="w"/>
                    <button string="Créer un ordre de réparation"
                            name="createOrderRepair"
                            type="object"
                            class="btn btn-primary"
                            invisible="state != 'on_hold'"
                            data-hotkey="w"/>

                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <div name="button_box">
                        <field name="order_id" invisible="1"/>
                        <button
                            name="openPickingViewForm"
                            type="object"
                            string="Réception" class="oe_stat_button" icon="fa-truck"
                        >
                            Réception
                        </button>
                        <button
                            name="openOrderRepairViewForm"
                            type="object"
                            invisible="not order_id"
                            string="Ordre de réparation" class="oe_stat_button" icon="fa-truck"
                        >
                            Ordre de réparation
                        </button>
                    </div>
                    <group>
                        <group>
                            <field name="tracking" invisible="1"/>
                            <field name="partner_id"/>
                            <field name="product_id"/>
                            <field name="lot_id" invisible="not tracking"/>
                            <field name="scheduled_date"/>
                        </group>
                        <group>
                            <field name="picking_id"/>
                            <field name="date_deadline"/>
                            <field name="reason_id" widget="many2many_tags"/>
                            <field name="comment"/>
                        </group>

                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_rma_stock_move_line_filter2" model="ir.ui.view">
        <field name="name">rma.stock.move.line.search</field>
        <field name="model">rma.stock.move.line</field>
        <field name="arch" type="xml">
            <search>
                <filter string="Numéro de série"
                        name="num_serial"
                />
                <filter string="On Hold"
                        name="on_hold"
                        domain="[('state', '=', 'on_hold')]"
                        context="{'default_on': True}"/>
            </search>
        </field>
    </record>

    <record id="action_open_stock_move_line_tree" model="ir.actions.act_window">
        <field name="name">Repair Orders</field>
        <field name="res_model">rma.stock.move.line</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="search_view_id" ref="view_rma_stock_move_line_filter2"/>
        <field name="context">{'search_default_on_hold': 1}</field>

        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No repair order found. Let's create one!
            </p>
            <p>
                In a repair order, you can detail the components you remove,
                add or replace and record the time you spent on the different
                operations.
            </p>
        </field>
    </record>

    <record id="action_open_stock_move_line_tree2" model="ir.actions.act_window">
        <field name="name">Repair Orders</field>
        <field name="res_model">rma.stock.move.line</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{'search_default_on_hold': 1}</field>

        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No repair order found. Let's create one!
            </p>
            <p>
                In a repair order, you can detail the components you remove,
                add or replace and record the time you spent on the different
                operations.
            </p>
        </field>
    </record>

    <!--Tree view-->
    <record id="view_repair_rma_stock_order_tree" model="ir.ui.view">
        <field name="name">repair.tag.tree</field>
        <field name="model">rma.stock.move.line</field>
        <field name="arch" type="xml">
            <tree string="Produits">
                <field name="partner_id"/>
                <field name="tracking" invisible="1"/>
                <field name="product_id"/>
                <field name="lot_id" invisible="not tracking"/>
                <field name="picking_id"/>
                <field name="scheduled_date"/>
                <field name="reason_id" widget="many2many_tags"/>
                <field name="comment"/>
            </tree>
        </field>
    </record>


    <!--view wizard-->
    <record model="ir.ui.view" id="rma_stock_move_line_wizard">
        <field name="name">Mouvement de stock</field>
        <field name="model">stock.move</field>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <group>
                        <field name="product_id" string="produit"/>
                        <field name="quantity" string="Quantité" readonly="1"/>
                        <field name="company_id" invisible="1"/>
                    </group>
                </group>
                <div class="row">
                    <field name="move_line_ids" options='{"no_open": True}'>
                        <tree edit="0" delete="0" create="0">
                            <field name="product_id" column_invisible="0" string="Produit"/>
                            <field name="lot_id" column_invisible="0" string="Lot/ Numéro de série"/>
                            <field name="reason_id" widget="many2many_tags"/>
                            <field name="comment"/>
                            <field name="state_repair" widget="selection" string="Etat du réparations"/>
                        </tree>
                        <form>
                            <group>
                                <group>
                                    <field name="company_id" invisible="1"/>
                                    <field name="product_id"/>
                                    <field name="reason_id"/>
                                </group>
                                <group>
                                    <field name="lot_id"/>
                                    <field name="comment"/>
                                </group>
                            </group>
                        </form>
                    </field>
                </div>
                <footer>
                    <!--                    <button name="valide_serial_number" string="Enregistrer" type="object" class="oe_highlight"/>-->
                    <!--                    <button string="Annuler" special="cancel" class="btn btn-light"/>-->
                </footer>
            </form>
        </field>
    </record>

    <!--action-->
    <record id="open_wizard_stock_picking_action" model="ir.actions.act_window">
        <field name="name">Mouvement du stock</field>
        <field name="res_model">stock.move</field>
        <field name="context">
            {'form_view_ref':'rma.rma_stock_move_line_wizard',
            'default_id': active_id,
            }
        </field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>


    <!--action open view page on RMA-->


    <!--    <menuitem id="product_to_repair_order_menu" name="Produits en attente de réparation"-->
    <!--              action="action_open_stock_move_line_tree" groups="stock.group_stock_user"-->
    <!--              parent="repair.menu_repair_order" sequence="1"/>-->
</odoo>
