<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="rma_finalization_view_search" model="ir.ui.view">
        <field name="model">rma.finalization</field>
        <field name="arch" type="xml">
            <search string="RMA Finalization Reasons">
                <field name="name" />
                <filter
                    string="Archived"
                    name="inactive"
                    domain="[('active','=',False)]"
                />
                <filter
                    string="Active"
                    name="active"
                    domain="[('active','!=',False)]"
                />
            </search>
        </field>
    </record>
    <record id="view_rma_finalization_form" model="ir.ui.view">
        <field name="name">Rma Finalization Reasons</field>
        <field name="model">rma.finalization</field>
        <field name="arch" type="xml">
            <form string="RMA Finalization">
                <sheet>
                    <widget
                        name="web_ribbon"
                        title="Archived"
                        bg_color="bg-danger"
                        invisible="active"
                    />
                    <group>
                        <field name="name" />
                        <field name="company_id" groups="base.group_multi_company" />
                        <field name="company_id" invisible="1" />
                        <field name="active" invisible="1" />
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="view_rma_finalization_list" model="ir.ui.view">
        <field name="name">RMA Finalization Reasons</field>
        <field name="model">rma.finalization</field>
        <field eval="6" name="priority" />
        <field name="arch" type="xml">
            <tree>
                <field name="name" />
                <field name="company_id" groups="base.group_multi_company" />
                <field name="company_id" invisible="1" />
            </tree>
        </field>
    </record>
    <record id="action_rma_finalization" model="ir.actions.act_window">
        <field name="name">RMA Finalization Reasons</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">rma.finalization</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
            Create a new RMA finalization
            </p>
            <p>
            Manage RMA finalization reasons to better classify them for tracking and analysis purposes.
            </p>
        </field>
    </record>
    <menuitem
        id="rma_configuration_rma_finalization_menu"
        name="RMA Finalization Reasons"
        parent="rma_configuration_menu"
        action="action_rma_finalization"
        groups="rma.group_rma_manual_finalization"
    />
</odoo>
