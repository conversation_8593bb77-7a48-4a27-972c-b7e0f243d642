<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2020 Tecnativa - <PERSON>
     Copyright 2023 Tecnativa - Pedro <PERSON>ez<PERSON>
     License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl). -->
<odoo>
    <record id="rma_view_search" model="ir.ui.view">
        <field name="name">rma.view.search</field>
        <field name="model">rma</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="origin"/>
                <field name="user_id"/>
                <field name="product_id"/>
                <field name="tag_ids"/>
                <filter
                    string="Awaiting Action"
                    name="waiting_action"
                    domain="[('state', 'in', ['waiting_return', 'waiting_replacement', 'confirmed'])]"
                />
                <filter
                    string="Processed"
                    name="processed"
                    domain="[('state', 'in', ['received', 'refunded', 'replaced', 'finished'])]"
                />
                <filter
                    string="Closed"
                    name="closed"
                    domain="[('state', 'in', ['locked', 'cancelled'])]"
                />
                <separator/>
                <filter
                    name="draft_filter"
                    string="Draft"
                    domain="[('state','=', 'draft')]"
                />
                <filter
                    name="confirmed_filter"
                    string="Confirmed"
                    domain="[('state','=', 'confirmed')]"
                />
                <filter
                    name="received_filter"
                    string="Received"
                    domain="[('state','=', 'received')]"
                />
                <separator/>
                <filter
                    string="Unresolved RMAs"
                    name="undone_rma"
                    domain="[('state', 'not in', ['refunded', 'returned', 'replaced', 'locked', 'cancelled', 'finished'])]"
                    help="RMAs yet to be fully processed"
                />
                <filter
                    string="Late RMAs"
                    name="late_rma"
                    domain="[('deadline', '&lt;', context_today().strftime('%Y-%m-%d')), ('state', 'not in', ['refunded', 'returned', 'replaced', 'locked', 'cancelled', 'finished'])]"
                    help="RMAs which deadline has passed"
                />
                <separator/>
                <filter string="RMA Date" name="filter_rma_date" date="date"/>
                <filter
                    string="RMA Deadline"
                    name="filter_rma_deadline"
                    date="deadline"
                />
                <filter
                    name="no_user_id_filter"
                    string="Unassigned RMAs"
                    domain="[('user_id','=', False)]"
                />
                <group string="Group By" name="group_by">
                    <filter
                        string="Partner"
                        name="partner_id_group_by"
                        context="{'group_by':'partner_id'}"
                    />
                    <filter
                        string="Responsible"
                        name="user_id_group_by"
                        context="{'group_by':'user_id'}"
                    />
                    <filter
                        string="State"
                        name="state_group_by"
                        context="{'group_by':'state'}"
                    />
                    <filter
                        string="Date"
                        name="date_group_by"
                        context="{'group_by':'date'}"
                    />
                    <filter
                        string="Deadline"
                        name="deadline_group_by"
                        context="{'group_by':'deadline'}"
                    />
                </group>
            </search>
        </field>
    </record>
    <record id="rma_view_tree" model="ir.ui.view">
        <field name="name">rma.view.tree</field>
        <field name="model">rma</field>
        <field name="arch" type="xml">
            <tree
                decoration-muted="state in ['cancelled', 'locked']"
                decoration-bf="state == 'draft' and product_id == False"
                decoration-danger="deadline and (deadline &lt; current_date)"
            >
                <field name="name" width="100px"/>
                <field name="origin"/>
                <field name="user_id"/>
                <field name="partner_id"/>
                <field name="product_id"/>
                <field name="product_uom_qty"/>
                <field name="product_uom" groups="uom.group_uom"/>
                <field name="date"/>
                <field name="deadline"/>
                <field name="finalization_id" optional="hide"/>
                <field name="state"/>
            </tree>
        </field>
    </record>
    <record id="rma_view_form" model="ir.ui.view">
        <field name="name">rma.view.form</field>
        <field name="model">rma</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button
                        name="%(portal.portal_share_action)d"
                        string="Share"
                        type="action"
                        class="oe_highlight oe_read_only"
                    />
                    <button
                        type="object"
                        string="Send by Email"
                        name="action_rma_send"
                        invisible="sent or state not in ['draft', 'confirmed', 'received']"
                        class="btn-primary"
                    />
                    <button
                        type="object"
                        string="Send by Mail"
                        name="action_rma_send"
                        invisible="not sent or state not in  ['draft', 'confirmed', 'received']"
                    />
                    <button
                        type="object"
                        string="Confirm"
                        name="action_confirm_duplicate"
                        invisible="state != 'draft'"
                        class="btn-primary"
                    />
                    <button
                        type="object"
                        string="To Refund"
                        name="action_refund"
                        invisible="not can_be_refunded"
                        class="btn-primary"
                    />
                    <button
                        type="object"
                        string="Replace"
                        name="action_replace"
                        invisible="not can_be_replaced"
                        class="btn-primary"
                    />
                    <button
                        type="object"
                        string="Return to customer"
                        name="action_return"
                        invisible="not can_be_returned"
                        class="btn-primary"
                    />
                    <button
                        type="object"
                        string="Split"
                        name="action_split"
                        invisible="not can_be_split"
                    />
                    <button
                        type="object"
                        string="Cancel"
                        name="action_cancel"
                        confirm="Are you sure you want to cancel this RMA"
                        invisible="state not in ['draft', 'confirmed']"
                    />
                    <button
                        type="object"
                        string="Set to draft"
                        name="action_draft"
                        invisible="state != 'cancelled'"
                    />
                    <button
                        type="object"
                        string="Lock"
                        name="action_lock"
                        invisible="not can_be_locked"
                    />
                    <button
                        type="object"
                        string="Unlock"
                        name="action_unlock"
                        invisible="state != 'locked'"
                    />
                    <button type="object" string="Preview" name="action_preview"/>
                    <field
                        name="state"
                        widget="statusbar"
                        statusbar_visible="draft,confirmed,received"
                    />
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button
                            type="object"
                            name="action_view_receipt"
                            string="Receipt"
                            class="oe_stat_button"
                            icon="fa-truck"
                            invisible="not reception_move_id"
                        >
                        </button>
                        <button
                            type="object"
                            name="action_view_delivery"
                            class="oe_stat_button"
                            string="Receipt"
                            icon="fa-truck"
                            invisible="delivery_picking_count == 0"
                        >
                            <field
                                name="delivery_picking_count"
                                widget="statinfo"
                                string="Receipt"
                            />
                        </button>


                        <button class="oe_stat_button" icon="fa-wrench"
                                name="action_view_repairs" type="object"
                                invisible="nbr_repairs == 0">
                            <field name="nbr_repairs" string="Ordres de réparation" widget="statinfo"
                                   field_id="nbr_repairs_0"/>
                        </button>

                        <button
                            type="object"
                            string="Refund"
                            name="action_view_refund"
                            class="oe_stat_button"
                            icon="fa-pencil-square-o"
                            invisible="not refund_id"
                        >
                        </button>
                    </div>
                    <field name="state_repair" invisible="1"/>
                    <widget name="web_ribbon" title="Reçu" bg_color="text-bg-success"
                            invisible="state_repair != 'received'" widget_id="widget_1"/>
                    <widget name="web_ribbon" title="En réparation" bg_color="text-bg-warning"
                            invisible="state_repair != 'received'" widget_id="widget_1"/>
                    <widget name="web_ribbon" title="Terminé" bg_color="text-bg-info"
                            invisible="state_repair != 'finished'" widget_id="widget_1"/>
                    finished
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="state != 'draft'"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field
                                name="partner_id"
                                widget="res_partner_many2one"
                                context="{'search_default_customer':1, 'show_address': 1, 'show_vat': True}"
                                readonly="state != 'draft'"
                                options="{'always_reload': True}"
                            />
                            <field
                                name="partner_shipping_id"
                                readonly="state != 'draft'"
                                force_save="1"
                            />
                            <field
                                name="partner_invoice_id"
                                readonly="state not in ['draft', 'confirmed', 'received']"
                                force_save="1"
                            />
                            <field
                                name="picking_id"
                                options="{'no_create': True}"
                                readonly="state != 'draft'"
                            />
                            <field
                                name="move_id"
                                required="picking_id"
                                readonly="not picking_id or state != 'draft'"
                                options="{'no_create': True}"
                                force_save="1"
                            />
                            <field
                                name="location_id"
                                required="0"
                                invisible="0"
                                options="{'no_create': True}"
                                force_save="1"
                            />
                            <field
                                name="location_dest_id"
                                required="0"
                                invisible="1"
                                options="{'no_create': True}"
                                force_save="1"
                            />
                            <field
                                name="product_id"
                                force_save="1"
                                readonly="picking_id or state != 'draft'"
                            />
                            <field name="uom_category_id" invisible="1"/>
                            <label for="product_uom_qty"/>
                            <div class="o_row">
                                <field
                                    name="product_uom_qty"
                                    readonly="state != 'draft'"
                                    force_save="1"
                                />
                                <field
                                    name="product_uom"
                                    groups="uom.group_uom"
                                    domain="[('category_id', '=', uom_category_id)]"
                                    readonly="state != 'draft'"
                                    force_save="1"
                                />
                                <field name="product_uom" invisible="1"/>
                            </div>
                            <field
                                name="delivered_qty"
                                invisible="delivered_qty == 0.0"
                            />
                        </group>
                        <group>
                            <field string="Nombre de produits réparés" name="nbr_repairs_finished" readonly="1"/>
                            <field name="date" readonly="state != 'draft'"/>
                            <field
                                name="user_id"
                                readonly="state in ['locked', 'cancelled']"
                            />
                            <field
                                name="team_id"
                                readonly="state in ['locked', 'cancelled']"
                            />
                            <field
                                name="tag_ids"
                                widget="many2many_tags"
                                options="{'color_field': 'color', 'no_create_edit': True}"
                                placeholder="Tags..."
                            />
                            <field
                                name="origin"
                                readonly="state in ['locked', 'cancelled']"
                            />
                            <field name="operation_id"/>
                            <field
                                name="finalization_id"
                                readonly="1"
                                invisible="state != 'finished'"
                            />
                            <field
                                name="company_id"
                                options="{'no_create': True}"
                                groups="base.group_multi_company"
                                readonly="state in ['locked', 'cancelled']"
                            />
                            <field
                                name="company_id"
                                invisible="1"
                                readonly="state in ['locked', 'cancelled']"
                            />
                            <field
                                name="receipt_id"
                                invisible="1"
                            />
                        </group>
                    </group>
                    <notebook>
                        <page name="page_retour" string="Lignes de retour">
                            <!--                            <button name="%(action_rma_mass_lot_wizard)d" string="Saisie rapide multi-lots"-->
                            <!--                                    type="action" class="btn-secondary mr-2"/>-->
                            <!--                            <button name="%(action_rma_import_lines_wizard)d"-->
                            <!--                                    string="Importer lignes (CSV)"-->
                            <!--                                    style="margin-left:10px !important;"-->
                            <!--                                    type="action" class="ml-4 btn-secondary"/>-->
                            <field name="order_line">
                                <tree editable="bottom">
                                    <field name="product_id" required="1"/>
                                    <field name="tracking" column_invisible="1" invisible="1"/>
                                    <field name="quantity"/>
                                    <field name="reason_id" widget="many2many_tags" invisible="tracking == 'serial'"/>
                                    <field name="comment" invisible="tracking == 'serial'"/>
                                    <field name="state_repair" widget="selection" string="Etat du réparations"/>
                                    <button name="openViewSerialNumber" type="object" icon="fa-list" role="img"
                                            title="Assigner des numéros de série"
                                            invisible="tracking != 'serial'"
                                    />
                                    <!--                                    invisible="product_id.product_tmpl_id.tracking != 'serial'"-->
                                </tree>
                            </field>

                            <!--                                <tree editable="bottom">-->
                            <!--                                    <field name="product_id"/>-->
                            <!--                                    <field name="lot_id"/>-->
                            <!--                                    <field name="reason_id"/>-->
                            <!--                                    <field name="description"/>-->
                            <!--                                </tree>-->
                            <!--                                <form>-->
                            <!--                                    <group>-->
                            <!--                                        <field name="product_id"/>-->
                            <!--                                        <field name="lot_id"/>-->
                            <!--                                        <field name="reason_id"/>-->
                            <!--                                        <field name="description"/>-->
                            <!--                                    </group>-->
                            <!--                                </form>-->
                            <!--                            </field>-->
                        </page>
                        <page name="page_other" string="Other Information">
                            <group>
                                <group>
                                    <field
                                        name="procurement_group_id"
                                        readonly="state not in ['draft', 'confirmed', 'received']"
                                    />
                                    <field
                                        name="location_id"
                                        options="{'no_create': True, 'no_open': True}"
                                        groups="stock.group_stock_multi_locations"
                                        readonly="state != 'draft'"
                                    />
                                    <field name="location_id" invisible="1"/>
                                </group>
                                <group>
                                    <field
                                        name="deadline"
                                        readonly="state in ['locked', 'cancelled']"
                                    />
                                    <field
                                        name="priority"
                                        widget="priority"
                                        readonly="state != 'draft'"
                                    />
                                    <field name="origin_split_rma_id" invisible="1"/>
                                </group>
                            </group>
                            <group>
                                <field
                                    name="description"
                                    widget="html"
                                    colspan="4"
                                    readonly="state in ['locked', 'cancelled']"
                                />
                            </group>
                        </page>
                    </notebook>
                    <field name="sent" invisible="1"/>
                    <field name="reception_move_id" invisible="1"/>
                    <field name="refund_id" invisible="1"/>
                    <field name="can_be_refunded" invisible="1"/>
                    <field name="can_be_returned" invisible="1"/>
                    <field name="can_be_replaced" invisible="1"/>
                    <field name="can_be_split" invisible="1"/>
                    <field name="can_be_locked" invisible="1"/>
                    <field name="can_be_finished" invisible="1"/>
                    <field name="commercial_partner_id" invisible="1"/>
                    <field name="remaining_qty" invisible="1"/>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <record id="rma_finalization_form" model="ir.ui.view">
        <field name="model">rma</field>
        <field name="inherit_id" ref="rma.rma_view_form"/>
        <field name="arch" type="xml">
            <xpath
                expr="//form//header//button[@name='action_cancel']"
                position="before"
            >
                <button
                    type="object"
                    string="Finish"
                    name="action_finish"
                    class="btn-primary"
                    invisible="not can_be_finished"
                    groups="rma.group_rma_manual_finalization"
                />
            </xpath>
        </field>
    </record>
    <record id="rma_view_pivot" model="ir.ui.view">
        <field name="name">rma.pivot</field>
        <field name="model">rma</field>
        <field name="arch" type="xml">
            <pivot>
                <field name="date" type="row"/>
                <field name="product_uom_qty" type="measure"/>
                <field name="delivered_qty" type="measure"/>
            </pivot>
        </field>
    </record>
    <record id="rma_view_calendar" model="ir.ui.view">
        <field name="name">rma.calendar</field>
        <field name="model">rma</field>
        <field name="arch" type="xml">
            <calendar date_start="date" mode="month" color="state">
                <field name="name"/>
                <field name="partner_id"/>
                <field name="product_id"/>
                <field name="product_uom_qty" widget="monetary"/>
            </calendar>
        </field>
    </record>
    <record id="rma_refund_action_server" model="ir.actions.server">
        <field name="name">To Refund</field>
        <field name="model_id" ref="model_rma"/>
        <field name="binding_model_id" ref="model_rma"/>
        <field name="state">code</field>
        <field name="code">records.action_refund()</field>
    </record>
    <record id="rma_action" model="ir.actions.act_window">
        <field name="name">RMA</field>
        <field name="res_model">rma</field>
        <field name="view_mode">tree,form,pivot,calendar,activity</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Click to add a new RMA.
            </p>
        </field>
    </record>
    <record id="rma_orders_menu" model="ir.ui.menu">
        <field name="action" ref="rma_action"/>
    </record>
    <!--    <record id="rma_view_form_inherit_lines" model="ir.ui.view">-->
    <!--        <field name="name">rma.view.form.inherit.lines</field>-->
    <!--        <field name="model">rma</field>-->
    <!--        <field name="inherit_id" ref="rma.rma_view_form"/>-->
    <!--        <field name="arch" type="xml">-->
    <!--            <notebook position="inside">-->
    <!--                <page string="Lignes de retour">-->
    <!--                    <field name="line_ids">-->
    <!--                        <tree editable="bottom">-->
    <!--                            <field name="product_id"/>-->
    <!--                            <field name="lot_id"/>-->
    <!--                            <field name="reason_id"/>-->
    <!--                            <field name="description"/>-->
    <!--                        </tree>-->
    <!--                        <form>-->
    <!--                            <group>-->
    <!--                                <field name="product_id"/>-->
    <!--                                <field name="lot_id"/>-->
    <!--                                <field name="reason_id"/>-->
    <!--                                <field name="description"/>-->
    <!--                            </group>-->
    <!--                        </form>-->
    <!--                    </field>-->
    <!--                    <xpath expr="//page[@string='Lignes de retour']" position="before">-->
    <!--                        <button name="%(action_rma_mass_lot_wizard)d" string="Saisie rapide multi-lots" type="action" class="btn-secondary"/>-->
    <!--                        <button name="%(action_rma_import_lines_wizard)d" string="Importer lignes (CSV)" type="action" class="btn-secondary"/>-->
    <!--                    </xpath>-->
    <!--                    <xpath expr="//page[@string='Lignes de retour']" position="inside">-->
    <!--                        <group>-->
    <!--                            <field name="scan_lot" placeholder="Scanner ou saisir un N° de série"/>-->
    <!--                            <button name="action_scan_lot" string="Ajouter" type="object" class="btn-primary"/>-->
    <!--                        </group>-->
    <!--                    </xpath>-->
    <!--                </page>-->
    <!--            </notebook>-->
    <!--        </field>-->
    <!--    </record>-->
    <record id="rma_view_form_inherit_hide_product" model="ir.ui.view">
        <field name="name">rma.view.form.inherit.hide.product</field>
        <field name="model">rma</field>
        <field name="inherit_id" ref="rma.rma_view_form"/>
        <field name="arch" type="xml">
            <field name="product_id" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="product_uom_qty" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="product_uom" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
        </field>
    </record>
</odoo>
