This module allows you to manage [Return Merchandise Authorization
(RMA)](https://en.wikipedia.org/wiki/Return_merchandise_authorization).
RMA documents can be created from scratch, from a delivery order or from
an incoming email. Product receptions and returning delivery operations
of the RMA module are fully integrated with the Receipts and Deliveries
Operations of Odoo inventory core module. It also allows you to generate
refunds in the same way as Odoo generates it. Besides, you have full
integration of the RMA documents in the customer portal.
