<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2020 Tecnativa - <PERSON>
     Copyright 2023 Tecnativa - Pedro <PERSON>ez<PERSON>
     License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl). -->
<odoo>
    <record id="view_stock_return_picking_form" model="ir.ui.view">
        <field name="name">Return lines inherit RMA</field>
        <field name="model">stock.return.picking</field>
        <field name="inherit_id" ref="stock.view_stock_return_picking_form" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_return_moves']//tree" position="inside">
                <field
                    name="rma_operation_id"
                    required="parent.create_rma and quantity>0"
                    column_invisible="not parent.create_rma"
                />
            </xpath>
            <field name="product_return_moves" position="before">
                <group name="group_rma">
                    <field
                        name="create_rma"
                        invisible="picking_type_code != 'outgoing'"
                    />
                    <field name="rma_operation_id" invisible="not create_rma" />
                    <field name="rma_location_ids" invisible="1" />
                    <field name="picking_id" invisible="1" />
                    <field name="picking_type_code" invisible="1" />
                    <field name="location_id" invisible="1" />
                </group>
            </field>
        </field>
    </record>
</odoo>
