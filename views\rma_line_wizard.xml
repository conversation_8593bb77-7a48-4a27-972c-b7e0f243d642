<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <!--view wizard-->
        <record model="ir.ui.view" id="rma_order_line_wizard">
            <field name="name">Mouvement de stock</field>
            <field name="model">rma.order.line</field>
            <field name="type">form</field>
            <field name="arch" type="xml">
                <form>
                    <group>
                        <group>
                            <field name="product_id" string="produit"/>
                            <field name="quantity" string="Quantité" readonly="1"/>
                        </group>
                    </group>
                    <div class="row">
                        <field name="serail_number" context="{'default_product_id': product_id}">
                            <tree editable="bottom">
                                <field name="company_id" column_invisible="1"/>
                                <field name="product_id" column_invisible="1" string="Produit"/>
                                <field name="name" string="Lot/numéro de série"/>
                                <field name="reason_id" widget="many2many_tags"/>
                                <field name="comment"/>
                            </tree>
                        </field>
                    </div>
                    <footer>
                        <button name="valide_serial_number" string="Enregistrer" type="object" class="oe_highlight"/>
                        <button string="Annuler" special="cancel" class="btn btn-light"/>
                    </footer>
                </form>
            </field>
        </record>
        <!--action-->
        <record id="open_wizard_input_serial_number_action" model="ir.actions.act_window">
            <field name="name">Mouvement de stock</field>
            <field name="res_model">rma.order.line</field>
            <field name="context">
                {'form_view_ref':'rma.rma_order_line_wizard',
                'default_id': active_id,
                'default_res_ids': active_ids
                }
            </field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>
    </data>
</odoo>
