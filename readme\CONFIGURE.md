If you want RMAs to be created from incoming emails, you need to:

1.  Go to *Settings \> General Settings*.
2.  Check 'External Email Servers' checkbox under *Discuss* section.
3.  Set an 'alias domain' and an incoming server.
4.  Go to *RMA \> Configuration \> RMA Team* and select a team or create
    a new one.
5.  Go to 'Email' tab and set an 'Email Alias'.

If you want to manually finish RMAs, you need to:

1.  Go to *Settings \> Inventory*.
2.  Set *Finish RMAs manually* checkbox on.

By default, returns to customer are grouped by shipping address,
warehouse and company. If you want to avoid this grouping you can:

1.  Go to *Settings \> Inventory*.
2.  Set *Group RMA returns by customer address and warehouse* checkbox
    off.

The users will still be able to group those pickings from the wizard.
