from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class RmaMassLotWizard(models.TransientModel):
    _name = 'rma.mass.lot.wizard'
    _description = 'Assistant <PERSON><PERSON>e Multi-Lots'

    rma_id = fields.Many2one('rma', required=True)
    product_id = fields.Many2one('product.product', required=True, string="Produit")
    reason_id = fields.Many2one('rma.reason', string="Raison du retour")
    lot_lines = fields.Text("Numéros de série (un par ligne)")
    comment = fields.Text("Commentaire (optionnel)")


    def action_add_lots(self):
        self.ensure_one()
        if not self.lot_lines:
            raise UserError(_("Veuillez saisir au moins un numéro de série."))
        lot_names = [l.strip() for l in self.lot_lines.splitlines() if l.strip()]
        for lot_name in lot_names:
            lot = self.env['stock.lot'].search([('name', '=', lot_name), ('product_id', '=', self.product_id.id)],
                                               limit=1)
            if not lot:
                lot = self.env['stock.lot'].create({'name': lot_name, 'product_id': self.product_id.id})
            _logger.info("Création rma.line: rma=%s, product=%s, lot=%s, reason=%s", self.rma_id, self.product_id, lot,
                         self.reason_id)
            self.env['rma.line'].create({
                'rma_id': self.rma_id.id,
                'product_id': self.product_id.id,
                'lot_id': lot.id,
                'reason_id': self.reason_id.id if self.reason_id else False,
                'description': self.comment or '',
            })
        return {'type': 'ir.actions.act_window_close'}
